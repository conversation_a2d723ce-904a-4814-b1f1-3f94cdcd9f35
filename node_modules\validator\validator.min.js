/*!
 * Copyright (c) 2018 <PERSON> <<EMAIL>>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.validator=e()}(this,function(){"use strict";function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,c=t[Symbol.iterator]();!(n=(o=c.next()).done)&&(r.push(o.value),!e||r.length!==e);n=!0);}catch(t){i=!0,a=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw a}}return r}}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){if(t){if("string"==typeof t)return n(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(r="Object"===r&&t.constructor?t.constructor.name:r)||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(r=c(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,e=function(){};return{s:e,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:e}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,o=!1;return{s:function(){r=t[Symbol.iterator]()},n:function(){var t=r.next();return a=t.done,t},e:function(t){o=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw i}}}}function u(t){if(!("string"==typeof t||t instanceof String)){var e=a(t);throw null===t?e="null":"object"===e&&(e=t.constructor.name),new TypeError("Expected a string but received a ".concat(e))}}function i(t){return u(t),t=Date.parse(t),isNaN(t)?null:new Date(t)}for(var t,o={"en-US":/^[A-Z]+$/i,"az-AZ":/^[A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[А-Я]+$/i,"cs-CZ":/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[A-ZÆØÅ]+$/i,"de-DE":/^[A-ZÄÖÜß]+$/i,"el-GR":/^[Α-ώ]+$/i,"es-ES":/^[A-ZÁÉÍÑÓÚÜ]+$/i,"fa-IR":/^[ابپتثجچحخدذرزژسشصضطظعغفقکگلمنوهی]+$/i,"fi-FI":/^[A-ZÅÄÖ]+$/i,"fr-FR":/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"nb-NO":/^[A-ZÆØÅ]+$/i,"nl-NL":/^[A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[A-ZÆØÅ]+$/i,"hu-HU":/^[A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"pl-PL":/^[A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[А-ЯЁ]+$/i,"kk-KZ":/^[А-ЯЁ\u04D8\u04B0\u0406\u04A2\u0492\u04AE\u049A\u04E8\u04BA]+$/i,"sl-SI":/^[A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๐\s]+$/i,"tr-TR":/^[A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[А-ЩЬЮЯЄIЇҐі]+$/i,"vi-VN":/^[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,"ko-KR":/^[ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[א-ת]+$/,fa:/^['آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,"hi-IN":/^[\u0900-\u0961]+[\u0972-\u097F]*$/i,"si-LK":/^[\u0D80-\u0DFF]+$/},s={"en-US":/^[0-9A-Z]+$/i,"az-AZ":/^[0-9A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[0-9А-Я]+$/i,"cs-CZ":/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[0-9A-ZÆØÅ]+$/i,"de-DE":/^[0-9A-ZÄÖÜß]+$/i,"el-GR":/^[0-9Α-ω]+$/i,"es-ES":/^[0-9A-ZÁÉÍÑÓÚÜ]+$/i,"fi-FI":/^[0-9A-ZÅÄÖ]+$/i,"fr-FR":/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[0-9A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[0-9０-９ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"hu-HU":/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"nb-NO":/^[0-9A-ZÆØÅ]+$/i,"nl-NL":/^[0-9A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[0-9A-ZÆØÅ]+$/i,"pl-PL":/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[0-9A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[0-9А-ЯЁ]+$/i,"kk-KZ":/^[0-9А-ЯЁ\u04D8\u04B0\u0406\u04A2\u0492\u04AE\u049A\u04E8\u04BA]+$/i,"sl-SI":/^[0-9A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[0-9A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[0-9A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[0-9А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[0-9A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๙\s]+$/i,"tr-TR":/^[0-9A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[0-9А-ЩЬЮЯЄIЇҐі]+$/i,"ko-KR":/^[0-9ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[٠١٢٣٤٥٦٧٨٩0-9ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,"vi-VN":/^[0-9A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[0-9א-ת]+$/,fa:/^['0-9آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی۱۲۳۴۵۶۷۸۹۰']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣ০১২৩৪৫৬৭৮৯ৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,"hi-IN":/^[\u0900-\u0963]+[\u0966-\u097F]*$/i,"si-LK":/^[0-9\u0D80-\u0DFF]+$/},f={"en-US":".",ar:"٫"},e=["AU","GB","HK","IN","NZ","ZA","ZM"],A=0;A<e.length;A++)t="en-".concat(e[A]),o[t]=o["en-US"],s[t]=s["en-US"],f[t]=f["en-US"];for(var $,p=["AE","BH","DZ","EG","IQ","JO","KW","LB","LY","MA","QM","QA","SA","SD","SY","TN","YE"],h=0;h<p.length;h++)$="ar-".concat(p[h]),o[$]=o.ar,s[$]=s.ar,f[$]=f.ar;for(var g,S=["IR","AF"],E=0;E<S.length;E++)g="fa-".concat(S[E]),s[g]=s.fa,f[g]=f.ar;for(var m,v=["BD","IN"],Z=0;Z<v.length;Z++)m="bn-".concat(v[Z]),o[m]=o.bn,s[m]=s.bn,f[m]=f["en-US"];for(var I=["ar-EG","ar-LB","ar-LY"],R=["bg-BG","cs-CZ","da-DK","de-DE","el-GR","en-ZM","es-ES","fr-CA","fr-FR","id-ID","it-IT","ku-IQ","hi-IN","hu-HU","nb-NO","nn-NO","nl-NL","pl-PL","pt-PT","ru-RU","kk-KZ","si-LK","sl-SI","sr-RS@latin","sr-RS","sv-SE","tr-TR","uk-UA","vi-VN"],L=0;L<I.length;L++)f[I[L]]=f["en-US"];for(var M=0;M<R.length;M++)f[R[M]]=",";function B(t,e){u(t),e=e||{};var r=new RegExp("^(?:[-+])?(?:[0-9]+)?(?:\\".concat(e.locale?f[e.locale]:".","[0-9]*)?(?:[eE][\\+\\-]?(?:[0-9]+))?$"));if(""===t||"."===t||","===t||"-"===t||"+"===t)return!1;var n=parseFloat(t.replace(",","."));return r.test(t)&&(!e.hasOwnProperty("min")||n>=e.min)&&(!e.hasOwnProperty("max")||n<=e.max)&&(!e.hasOwnProperty("lt")||n<e.lt)&&(!e.hasOwnProperty("gt")||n>e.gt)}o["fr-CA"]=o["fr-FR"],s["fr-CA"]=s["fr-FR"],o["pt-BR"]=o["pt-PT"],s["pt-BR"]=s["pt-PT"],f["pt-BR"]=f["pt-PT"],o["pl-Pl"]=o["pl-PL"],s["pl-Pl"]=s["pl-PL"],f["pl-Pl"]=f["pl-PL"],o["fa-AF"]=o.fa;var C=Object.keys(f);function N(t){return B(t)?parseFloat(t):NaN}function F(t){return"object"===a(t)&&null!==t?t="function"==typeof t.toString?t.toString():"[object Object]":(null==t||isNaN(t)&&!t.length)&&(t=""),String(t)}function D(t,e){var r,n=0<arguments.length&&void 0!==t?t:{},i=1<arguments.length?e:void 0;for(r in i)void 0===n[r]&&(n[r]=i[r]);return n}var T={ignoreCase:!1,minOccurrences:1};function G(t,e){var r;u(t);e="object"===a(e)?(r=e.min||0,e.max):(r=e,arguments[2]),t=encodeURI(t).split(/%..|./).length-1;return r<=t&&(void 0===e||t<=e)}var O={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};function P(t,e){u(t),(e=D(e,O)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1));var r=(t=!0===e.allow_wildcard&&0===t.indexOf("*.")?t.substring(2):t).split("."),t=r[r.length-1];if(e.require_tld){if(r.length<2)return!1;if(!e.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(t))return!1;if(/\s/.test(t))return!1}return!(!e.allow_numeric_tld&&/^\d+$/.test(t))&&r.every(function(t){return!(63<t.length&&!e.ignore_max_length)&&(!!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(t)&&(!/[\uff01-\uff5e]/.test(t)&&(!/^-|-$/.test(t)&&!(!e.allow_underscores&&/_/.test(t)))))})}var H="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",b="(".concat(H,"[.]){3}").concat(H),_=new RegExp("^".concat(b,"$")),U="(?:[0-9a-fA-F]{1,4})",w=new RegExp("^("+"(?:".concat(U,":){7}(?:").concat(U,"|:)|")+"(?:".concat(U,":){6}(?:").concat(b,"|:").concat(U,"|:)|")+"(?:".concat(U,":){5}(?::").concat(b,"|(:").concat(U,"){1,2}|:)|")+"(?:".concat(U,":){4}(?:(:").concat(U,"){0,1}:").concat(b,"|(:").concat(U,"){1,3}|:)|")+"(?:".concat(U,":){3}(?:(:").concat(U,"){0,2}:").concat(b,"|(:").concat(U,"){1,4}|:)|")+"(?:".concat(U,":){2}(?:(:").concat(U,"){0,3}:").concat(b,"|(:").concat(U,"){1,5}|:)|")+"(?:".concat(U,":){1}(?:(:").concat(U,"){0,4}:").concat(b,"|(:").concat(U,"){1,6}|:)|")+"(?::((?::".concat(U,"){0,5}:").concat(b,"|(?::").concat(U,"){1,7}|:))")+")(%[0-9a-zA-Z-.:]{1,})?$");function K(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return u(t),(e=String(e))?"4"===e?_.test(t):"6"===e&&w.test(t):K(t,4)||K(t,6)}var y={allow_display_name:!1,allow_underscores:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},W=/^([^\x00-\x1F\x7F-\x9F\cX]+)</i,Y=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,x=/^[a-z\d]+$/,k=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,V=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A1-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,z=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i,X=254;function J(t,e){if(u(t),(e=D(e,y)).require_display_name||e.allow_display_name){var r=t.match(W);if(r){var n=r[1];if(t=t.replace(n,"").replace(/(^<|>$)/g,""),!function(t){var e=t.replace(/^"(.+)"$/,"$1");if(e.trim()){if(/[\.";<>]/.test(e)){if(e===t)return;if(!(e.split('"').length===e.split('\\"').length))return}return 1}}(n=n.endsWith(" ")?n.slice(0,-1):n))return!1}else if(e.require_display_name)return!1}if(!e.ignore_max_length&&t.length>X)return!1;r=t.split("@"),n=r.pop(),t=n.toLowerCase();if(e.host_blacklist.includes(t))return!1;if(0<e.host_whitelist.length&&!e.host_whitelist.includes(t))return!1;r=r.join("@");if(e.domain_specific_validation&&("gmail.com"===t||"googlemail.com"===t)){t=(r=r.toLowerCase()).split("+")[0];if(!G(t.replace(/\./g,""),{min:6,max:30}))return!1;for(var i=t.split("."),a=0;a<i.length;a++)if(!x.test(i[a]))return!1}if(!(!1!==e.ignore_max_length||G(r,{max:64})&&G(n,{max:254})))return!1;if(!P(n,{require_tld:e.require_tld,ignore_max_length:e.ignore_max_length,allow_underscores:e.allow_underscores})){if(!e.allow_ip_domain)return!1;if(!K(n)){if(!n.startsWith("[")||!n.endsWith("]"))return!1;n=n.slice(1,-1);if(0===n.length||!K(n))return!1}}if('"'===r[0])return r=r.slice(1,r.length-1),(e.allow_utf8_local_part?z:k).test(r);for(var o=e.allow_utf8_local_part?V:Y,c=r.split("."),s=0;s<c.length;s++)if(!o.test(c[s]))return!1;return!e.blacklisted_chars||-1===r.search(new RegExp("[".concat(e.blacklisted_chars,"]+"),"g"))}var j={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0},q=/^\[([^\]]+)\](?::([0-9]+))?$/;function Q(t,e){for(var r,n=0;n<e.length;n++){var i=e[n];if(t===i||(r=i,"[object RegExp]"===Object.prototype.toString.call(r)&&i.test(t)))return!0}return!1}var tt=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){4}([0-9a-fA-F]{2})$/,et=/^([0-9a-fA-F]){12}$/,rt=/^([0-9a-fA-F]{4}\.){2}([0-9a-fA-F]{4})$/,nt=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){6}([0-9a-fA-F]{2})$/,it=/^([0-9a-fA-F]){16}$/,at=/^([0-9a-fA-F]{4}\.){3}([0-9a-fA-F]{4})$/;var ot=/^\d{1,3}$/;var ct={format:"YYYY/MM/DD",delimiters:["/","-"],strictMode:!1};function st(e,r){if(r=D("string"==typeof r?{format:r}:r,ct),"string"==typeof e&&/(^(y{4}|y{2})[.\/-](m{1,2})[.\/-](d{1,2})$)|(^(m{1,2})[.\/-](d{1,2})[.\/-]((y{4}|y{2})$))|(^(d{1,2})[.\/-](m{1,2})[.\/-]((y{4}|y{2})$))/gi.test(r.format)){var t,n=r.delimiters.find(function(t){return-1!==r.format.indexOf(t)}),i=r.strictMode?n:r.delimiters.find(function(t){return-1!==e.indexOf(t)}),a={},o=d(function(t,e){for(var r=[],n=Math.min(t.length,e.length),i=0;i<n;i++)r.push([t[i],e[i]]);return r}(e.split(i),r.format.toLowerCase().split(n)));try{for(o.s();!(t=o.n()).done;){var c=l(t.value,2),s=c[0],u=c[1];if(s.length!==u.length)return!1;a[u.charAt(0)]=s}}catch(t){o.e(t)}finally{o.f()}i=a.y;if(2===a.y.length){n=parseInt(a.y,10);if(isNaN(n))return!1;i=(n<(new Date).getFullYear()%100?"20":"19").concat(a.y)}return new Date("".concat(i,"-").concat(a.m,"-").concat(a.d)).getDate()===+a.d}return!r.strictMode&&("[object Date]"===Object.prototype.toString.call(e)&&isFinite(e))}var ut={hourFormat:"hour24",mode:"default"},lt={hour24:{default:/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/,withSeconds:/^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/},hour12:{default:/^(0?[1-9]|1[0-2]):([0-5][0-9]) (A|P)M$/,withSeconds:/^(0?[1-9]|1[0-2]):([0-5][0-9]):([0-5][0-9]) (A|P)M$/}};var dt={loose:!1},ft=["true","false","1","0"],At=[].concat(ft,["yes","no"]);var $t="(([a-zA-Z]{2,3}(-".concat("([A-Za-z]{3}(-[A-Za-z]{3}){0,2})",")?)|([a-zA-Z]{5,8}))"),pt="(".concat("(\\d|[A-W]|[Y-Z]|[a-w]|[y-z])","(-[A-Za-z0-9]{2,8})+)"),ht="(x(-[A-Za-z0-9]{1,8})+)",gt="(".concat("((en-GB-oed)|(i-ami)|(i-bnn)|(i-default)|(i-enochian)|(i-hak)|(i-klingon)|(i-lux)|(i-mingo)|(i-navajo)|(i-pwn)|(i-tao)|(i-tay)|(i-tsu)|(sgn-BE-FR)|(sgn-BE-NL)|(sgn-CH-DE))","|").concat("((art-lojban)|(cel-gaulish)|(no-bok)|(no-nyn)|(zh-guoyu)|(zh-hakka)|(zh-min)|(zh-min-nan)|(zh-xiang))",")"),St="(-|_)",Et="".concat($t,"(").concat(St).concat("([A-Za-z]{4})",")?(").concat(St).concat("([A-Za-z]{2}|\\d{3})",")?(").concat(St).concat("([A-Za-z0-9]{5,8}|(\\d[A-Z-a-z0-9]{3}))",")*(").concat(St).concat(pt,")*(").concat(St).concat(ht,")?"),mt=new RegExp("(^".concat(ht,"$)|(^").concat(gt,"$)|(^").concat(Et,"$)"));H=Object.keys(o);var b=Object.keys(s),vt=/^[0-9]+$/;var Zt={AM:/^[A-Z]{2}\d{7}$/,AR:/^[A-Z]{3}\d{6}$/,AT:/^[A-Z]\d{7}$/,AU:/^[A-Z]\d{7}$/,AZ:/^[A-Z]{2,3}\d{7,8}$/,BE:/^[A-Z]{2}\d{6}$/,BG:/^\d{9}$/,BR:/^[A-Z]{2}\d{6}$/,BY:/^[A-Z]{2}\d{7}$/,CA:/^[A-Z]{2}\d{6}$/,CH:/^[A-Z]\d{7}$/,CN:/^G\d{8}$|^E(?![IO])[A-Z0-9]\d{7}$/,CY:/^[A-Z](\d{6}|\d{8})$/,CZ:/^\d{8}$/,DE:/^[CFGHJKLMNPRTVWXYZ0-9]{9}$/,DK:/^\d{9}$/,DZ:/^\d{9}$/,EE:/^([A-Z]\d{7}|[A-Z]{2}\d{7})$/,ES:/^[A-Z0-9]{2}([A-Z0-9]?)\d{6}$/,FI:/^[A-Z]{2}\d{7}$/,FR:/^\d{2}[A-Z]{2}\d{5}$/,GB:/^\d{9}$/,GR:/^[A-Z]{2}\d{7}$/,HR:/^\d{9}$/,HU:/^[A-Z]{2}(\d{6}|\d{7})$/,IE:/^[A-Z0-9]{2}\d{7}$/,IN:/^[A-Z]{1}-?\d{7}$/,ID:/^[A-C]\d{7}$/,IR:/^[A-Z]\d{8}$/,IS:/^(A)\d{7}$/,IT:/^[A-Z0-9]{2}\d{7}$/,JM:/^[Aa]\d{7}$/,JP:/^[A-Z]{2}\d{7}$/,KR:/^[MS]\d{8}$/,KZ:/^[a-zA-Z]\d{7}$/,LI:/^[a-zA-Z]\d{5}$/,LT:/^[A-Z0-9]{8}$/,LU:/^[A-Z0-9]{8}$/,LV:/^[A-Z0-9]{2}\d{7}$/,LY:/^[A-Z0-9]{8}$/,MT:/^\d{7}$/,MZ:/^([A-Z]{2}\d{7})|(\d{2}[A-Z]{2}\d{5})$/,MY:/^[AHK]\d{8}$/,MX:/^\d{10,11}$/,NL:/^[A-Z]{2}[A-Z0-9]{6}\d$/,NZ:/^([Ll]([Aa]|[Dd]|[Ff]|[Hh])|[Ee]([Aa]|[Pp])|[Nn])\d{6}$/,PH:/^([A-Z](\d{6}|\d{7}[A-Z]))|([A-Z]{2}(\d{6}|\d{7}))$/,PK:/^[A-Z]{2}\d{7}$/,PL:/^[A-Z]{2}\d{7}$/,PT:/^[A-Z]\d{6}$/,RO:/^\d{8,9}$/,RU:/^\d{9}$/,SE:/^\d{8}$/,SL:/^(P)[A-Z]\d{7}$/,SK:/^[0-9A-Z]\d{7}$/,TH:/^[A-Z]{1,2}\d{6,7}$/,TR:/^[A-Z]\d{8}$/,UA:/^[A-Z]{2}\d{6}$/,US:/^\d{9}$/};var It=/^(?:[-+]?(?:0|[1-9][0-9]*))$/,Rt=/^[-+]?[0-9]+$/;function Lt(t,e){u(t);var r=(e=e||{}).hasOwnProperty("allow_leading_zeroes")&&!e.allow_leading_zeroes?It:Rt,n=!e.hasOwnProperty("min")||t>=e.min,i=!e.hasOwnProperty("max")||t<=e.max,a=!e.hasOwnProperty("lt")||t<e.lt,e=!e.hasOwnProperty("gt")||t>e.gt;return r.test(t)&&n&&i&&a&&e}var Mt=/^[0-9]{15}$/,Bt=/^\d{2}-\d{6}-\d{6}-\d{1}$/;var Ct=/^[\x00-\x7F]+$/;var Nt=/[^\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var Ft=/[\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var Dt=/[^\x00-\x7F]/;var Tt,Gt,Ot=(Gt="i",Tt=(Tt=["^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)","(?:-((?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*))*))","?(?:\\+([0-9a-z-]+(?:\\.[0-9a-z-]+)*))?$"]).join(""),new RegExp(Tt,Gt));var Pt=/[\uD800-\uDBFF][\uDC00-\uDFFF]/;var Ht={force_decimal:!1,decimal_digits:"1,",locale:"en-US"},bt=["","-","+"];var _t=/^(0x|0h)?[0-9A-F]+$/i;function Ut(t){return u(t),_t.test(t)}var wt=/^(0o)?[0-7]+$/i;var Kt=/^#?([0-9A-F]{3}|[0-9A-F]{4}|[0-9A-F]{6}|[0-9A-F]{8})$/i;var yt=/^rgb\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){2}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\)$/,Wt=/^rgba\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)$/,Yt=/^rgb\((([0-9]%|[1-9][0-9]%|100%),){2}([0-9]%|[1-9][0-9]%|100%)\)$/,xt=/^rgba\((([0-9]%|[1-9][0-9]%|100%),){3}(0?\.\d|1(\.0)?|0(\.0)?)\)$/;var kt=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(,(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}(,((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?))?\)$/i,Vt=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(\s(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}\s?(\/\s((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?)\s?)?\)$/i;var zt=/^[A-Z]{2}[0-9A-Z]{3}\d{2}\d{5}$/;var Xt={AD:/^(AD[0-9]{2})\d{8}[A-Z0-9]{12}$/,AE:/^(AE[0-9]{2})\d{3}\d{16}$/,AL:/^(AL[0-9]{2})\d{8}[A-Z0-9]{16}$/,AT:/^(AT[0-9]{2})\d{16}$/,AZ:/^(AZ[0-9]{2})[A-Z0-9]{4}\d{20}$/,BA:/^(BA[0-9]{2})\d{16}$/,BE:/^(BE[0-9]{2})\d{12}$/,BG:/^(BG[0-9]{2})[A-Z]{4}\d{6}[A-Z0-9]{8}$/,BH:/^(BH[0-9]{2})[A-Z]{4}[A-Z0-9]{14}$/,BR:/^(BR[0-9]{2})\d{23}[A-Z]{1}[A-Z0-9]{1}$/,BY:/^(BY[0-9]{2})[A-Z0-9]{4}\d{20}$/,CH:/^(CH[0-9]{2})\d{5}[A-Z0-9]{12}$/,CR:/^(CR[0-9]{2})\d{18}$/,CY:/^(CY[0-9]{2})\d{8}[A-Z0-9]{16}$/,CZ:/^(CZ[0-9]{2})\d{20}$/,DE:/^(DE[0-9]{2})\d{18}$/,DK:/^(DK[0-9]{2})\d{14}$/,DO:/^(DO[0-9]{2})[A-Z]{4}\d{20}$/,EE:/^(EE[0-9]{2})\d{16}$/,EG:/^(EG[0-9]{2})\d{25}$/,ES:/^(ES[0-9]{2})\d{20}$/,FI:/^(FI[0-9]{2})\d{14}$/,FO:/^(FO[0-9]{2})\d{14}$/,FR:/^(FR[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,GB:/^(GB[0-9]{2})[A-Z]{4}\d{14}$/,GE:/^(GE[0-9]{2})[A-Z0-9]{2}\d{16}$/,GI:/^(GI[0-9]{2})[A-Z]{4}[A-Z0-9]{15}$/,GL:/^(GL[0-9]{2})\d{14}$/,GR:/^(GR[0-9]{2})\d{7}[A-Z0-9]{16}$/,GT:/^(GT[0-9]{2})[A-Z0-9]{4}[A-Z0-9]{20}$/,HR:/^(HR[0-9]{2})\d{17}$/,HU:/^(HU[0-9]{2})\d{24}$/,IE:/^(IE[0-9]{2})[A-Z0-9]{4}\d{14}$/,IL:/^(IL[0-9]{2})\d{19}$/,IQ:/^(IQ[0-9]{2})[A-Z]{4}\d{15}$/,IR:/^(IR[0-9]{2})0\d{2}0\d{18}$/,IS:/^(IS[0-9]{2})\d{22}$/,IT:/^(IT[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,JO:/^(JO[0-9]{2})[A-Z]{4}\d{22}$/,KW:/^(KW[0-9]{2})[A-Z]{4}[A-Z0-9]{22}$/,KZ:/^(KZ[0-9]{2})\d{3}[A-Z0-9]{13}$/,LB:/^(LB[0-9]{2})\d{4}[A-Z0-9]{20}$/,LC:/^(LC[0-9]{2})[A-Z]{4}[A-Z0-9]{24}$/,LI:/^(LI[0-9]{2})\d{5}[A-Z0-9]{12}$/,LT:/^(LT[0-9]{2})\d{16}$/,LU:/^(LU[0-9]{2})\d{3}[A-Z0-9]{13}$/,LV:/^(LV[0-9]{2})[A-Z]{4}[A-Z0-9]{13}$/,MA:/^(MA[0-9]{26})$/,MC:/^(MC[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,MD:/^(MD[0-9]{2})[A-Z0-9]{20}$/,ME:/^(ME[0-9]{2})\d{18}$/,MK:/^(MK[0-9]{2})\d{3}[A-Z0-9]{10}\d{2}$/,MR:/^(MR[0-9]{2})\d{23}$/,MT:/^(MT[0-9]{2})[A-Z]{4}\d{5}[A-Z0-9]{18}$/,MU:/^(MU[0-9]{2})[A-Z]{4}\d{19}[A-Z]{3}$/,MZ:/^(MZ[0-9]{2})\d{21}$/,NL:/^(NL[0-9]{2})[A-Z]{4}\d{10}$/,NO:/^(NO[0-9]{2})\d{11}$/,PK:/^(PK[0-9]{2})[A-Z0-9]{4}\d{16}$/,PL:/^(PL[0-9]{2})\d{24}$/,PS:/^(PS[0-9]{2})[A-Z0-9]{4}\d{21}$/,PT:/^(PT[0-9]{2})\d{21}$/,QA:/^(QA[0-9]{2})[A-Z]{4}[A-Z0-9]{21}$/,RO:/^(RO[0-9]{2})[A-Z]{4}[A-Z0-9]{16}$/,RS:/^(RS[0-9]{2})\d{18}$/,SA:/^(SA[0-9]{2})\d{2}[A-Z0-9]{18}$/,SC:/^(SC[0-9]{2})[A-Z]{4}\d{20}[A-Z]{3}$/,SE:/^(SE[0-9]{2})\d{20}$/,SI:/^(SI[0-9]{2})\d{15}$/,SK:/^(SK[0-9]{2})\d{20}$/,SM:/^(SM[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,SV:/^(SV[0-9]{2})[A-Z0-9]{4}\d{20}$/,TL:/^(TL[0-9]{2})\d{19}$/,TN:/^(TN[0-9]{2})\d{20}$/,TR:/^(TR[0-9]{2})\d{5}[A-Z0-9]{17}$/,UA:/^(UA[0-9]{2})\d{6}[A-Z0-9]{19}$/,VA:/^(VA[0-9]{2})\d{18}$/,VG:/^(VG[0-9]{2})[A-Z0-9]{4}\d{16}$/,XK:/^(XK[0-9]{2})\d{16}$/};function Jt(t,e){var r=t.replace(/[\s\-]+/gi,"").toUpperCase(),n=r.slice(0,2).toUpperCase(),t=n in Xt;if(e.whitelist){if(0<e.whitelist.filter(function(t){return!(t in Xt)}).length)return!1;if(!e.whitelist.includes(n))return!1}if(e.blacklist&&e.blacklist.includes(n))return!1;return t&&Xt[n].test(r)}var U=Object.keys(Xt),jt=new Set(["AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CR","CU","CV","CW","CX","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","EH","ER","ES","ET","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","YE","YT","ZA","ZM","ZW"]);var qt=jt,Qt=/^[A-Za-z]{6}[A-Za-z0-9]{2}([A-Za-z0-9]{3})?$/;var te=/^[a-f0-9]{32}$/;var ee={md5:32,md4:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8};var re=/[^A-Z0-9+\/=]/i,ne=/^[A-Z0-9_\-]*$/i,ie={urlSafe:!1};function ae(t,e){u(t),e=D(e,ie);var r=t.length;if(e.urlSafe)return ne.test(t);if(r%4!=0||re.test(t))return!1;e=t.indexOf("=");return-1===e||e===r-1||e===r-2&&"="===t[r-1]}var oe={allow_primitives:!1};var ce={ignore_whitespace:!1};var se={1:/^[0-9A-F]{8}-[0-9A-F]{4}-1[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,2:/^[0-9A-F]{8}-[0-9A-F]{4}-2[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i};function ue(t){u(t);for(var e,r,n=t.replace(/[- ]+/g,""),i=0,a=n.length-1;0<=a;a--)e=n.substring(a,a+1),e=parseInt(e,10),i+=r&&10<=(e*=2)?e%10+1:e,r=!r;return!(i%10!=0||!n)}var le={amex:/^3[47][0-9]{13}$/,dinersclub:/^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,discover:/^6(?:011|5[0-9][0-9])[0-9]{12,15}$/,jcb:/^(?:2131|1800|35\d{3})\d{11}$/,mastercard:/^5[1-5][0-9]{2}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}$/,unionpay:/^(6[27][0-9]{14}|^(81[0-9]{14,17}))$/,visa:/^(?:4[0-9]{12})(?:[0-9]{3,6})?$/},de=function(){var t,e=[];for(t in le)le.hasOwnProperty(t)&&e.push(le[t]);return e}();var fe={PL:function(t){u(t);var n={1:1,2:3,3:7,4:9,5:1,6:3,7:7,8:9,9:1,10:3,11:0};if(null!=t&&11===t.length&&Lt(t,{allow_leading_zeroes:!0})){var e=t.split("").slice(0,-1).reduce(function(t,e,r){return t+Number(e)*n[r+1]},0)%10,t=Number(t.charAt(t.length-1));if(0==e&&0===t||t===10-e)return!0}return!1},ES:function(t){u(t);var e={X:0,Y:1,Z:2},r=t.trim().toUpperCase();if(!/^[0-9X-Z][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/.test(r))return!1;t=r.slice(0,-1).replace(/[X,Y,Z]/g,function(t){return e[t]});return r.endsWith(["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][t%23])},FI:function(t){if(u(t),11!==t.length)return!1;if(!t.match(/^\d{6}[\-A\+]\d{3}[0-9ABCDEFHJKLMNPRSTUVWXY]{1}$/))return!1;return"0123456789ABCDEFHJKLMNPRSTUVWXY"[(1e3*parseInt(t.slice(0,6),10)+parseInt(t.slice(7,10),10))%31]===t.slice(10,11)},IN:function(t){var r=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],n=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],t=t.trim();if(!/^[1-9]\d{3}\s?\d{4}\s?\d{4}$/.test(t))return!1;var i=0;return t.replace(/\s/g,"").split("").map(Number).reverse().forEach(function(t,e){i=r[i][n[e%8][t]]}),0===i},IR:function(t){if(!t.match(/^\d{10}$/))return!1;if(t="0000".concat(t).slice(t.length-6),0===parseInt(t.slice(3,9),10))return!1;for(var e=parseInt(t.slice(9,10),10),r=0,n=0;n<9;n++)r+=parseInt(t.slice(n,n+1),10)*(10-n);return(r%=11)<2&&e===r||2<=r&&e===11-r},IT:function(t){return 9===t.length&&("CA00000AA"!==t&&-1<t.search(/C[A-Z][0-9]{5}[A-Z]{2}/i))},NO:function(t){var e=t.trim();if(isNaN(Number(e)))return!1;if(11!==e.length)return!1;if("00000000000"===e)return!1;var r=e.split("").map(Number),t=(11-(3*r[0]+7*r[1]+6*r[2]+ +r[3]+8*r[4]+9*r[5]+4*r[6]+5*r[7]+2*r[8])%11)%11,e=(11-(5*r[0]+4*r[1]+3*r[2]+2*r[3]+7*r[4]+6*r[5]+5*r[6]+4*r[7]+3*r[8]+2*t)%11)%11;return t===r[9]&&e===r[10]},TH:function(t){if(!t.match(/^[1-8]\d{12}$/))return!1;for(var e=0,r=0;r<12;r++)e+=parseInt(t[r],10)*(13-r);return t[12]===((11-e%11)%10).toString()},LK:function(t){return!(10!==t.length||!/^[1-9]\d{8}[vx]$/i.test(t))||!(12!==t.length||!/^[1-9]\d{11}$/i.test(t))},"he-IL":function(t){t=t.trim();if(!/^\d{9}$/.test(t))return!1;for(var e,r=t,n=0,i=0;i<r.length;i++)n+=9<(e=Number(r[i])*(i%2+1))?e-9:e;return n%10==0},"ar-LY":function(t){t=t.trim();return!!/^(1|2)\d{11}$/.test(t)},"ar-TN":function(t){t=t.trim();return!!/^\d{8}$/.test(t)},"zh-CN":function(t){function n(t){return r.includes(t)}function i(t){var e=parseInt(t.substring(0,4),10),r=parseInt(t.substring(4,6),10),n=parseInt(t.substring(6),10);return!((t=new Date(e,r-1,n))>new Date)&&(t.getFullYear()===e&&t.getMonth()===r-1&&t.getDate()===n)}function a(t){return function(t){for(var e=t.substring(0,17),r=0,n=0;n<17;n++)r+=parseInt(e.charAt(n),10)*parseInt(o[n],10);return c[r%11]}(t)===t.charAt(17).toUpperCase()}var e,r=["11","12","13","14","15","21","22","23","31","32","33","34","35","36","37","41","42","43","44","45","46","50","51","52","53","54","61","62","63","64","65","71","81","82","91"],o=["7","9","10","5","8","4","2","1","6","3","7","9","10","5","8","4","2"],c=["1","0","X","9","8","7","6","5","4","3","2"];return!!/^\d{15}|(\d{17}(\d|x|X))$/.test(e=t)&&(15===e.length?function(t){var e=/^[1-9]\d{7}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}$/.test(t);if(!e)return!1;var r=t.substring(0,2);if(!(e=n(r)))return!1;t="19".concat(t.substring(6,12));return!!(e=i(t))}:function(t){var e=/^[1-9]\d{5}[1-9]\d{3}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}(\d|x|X)$/.test(t);if(!e)return!1;var r=t.substring(0,2);if(!(e=n(r)))return!1;r=t.substring(6,14);return!!(e=i(r))&&a(t)})(e)},"zh-HK":function(t){var e=/^[0-9]$/;if(t=(t=t.trim()).toUpperCase(),!/^[A-Z]{1,2}[0-9]{6}((\([0-9A]\))|(\[[0-9A]\])|([0-9A]))$/.test(t))return!1;8===(t=t.replace(/\[|\]|\(|\)/g,"")).length&&(t="3".concat(t));for(var r=0,n=0;n<=7;n++)r+=(e.test(t[n])?t[n]:(t[n].charCodeAt(0)-55)%11)*(9-n);return(0===(r%=11)?"0":1===r?"A":String(11-r))===t[t.length-1]},"zh-TW":function(t){var n={A:10,B:11,C:12,D:13,E:14,F:15,G:16,H:17,I:34,J:18,K:19,L:20,M:21,N:22,O:35,P:23,Q:24,R:25,S:26,T:27,U:28,V:29,W:32,X:30,Y:31,Z:33},t=t.trim().toUpperCase();return!!/^[A-Z][0-9]{9}$/.test(t)&&Array.from(t).reduce(function(t,e,r){if(0!==r)return 9===r?(10-t%10-Number(e))%10==0:t+Number(e)*(9-r);e=n[e];return e%10*9+Math.floor(e/10)},0)}};var Ae=8,$e=14,pe=/^(\d{8}|\d{13}|\d{14})$/;function he(r){var t=10-r.slice(0,-1).split("").map(function(t,e){return Number(t)*(t=r.length,e=e,t===Ae||t===$e?e%2==0?3:1:e%2==0?1:3)}).reduce(function(t,e){return t+e},0)%10;return t<10?t:0}var ge=/^[A-Z]{2}[0-9A-Z]{9}[0-9]$/;var Se=/^(?:[0-9]{9}X|[0-9]{10})$/,Ee=/^(?:[0-9]{13})$/,me=[1,3];function ve(t){for(var e=10,r=0;r<t.length-1;r++)e=(parseInt(t[r],10)+e)%10==0?9:(parseInt(t[r],10)+e)%10*2%11;return(e=1===e?0:11-e)===parseInt(t[10],10)}function Ze(t){for(var e,r=0,n=!1,i=t.length-1;0<=i;i--)r+=n?9<(e=2*parseInt(t[i],10))?e.toString().split("").map(function(t){return parseInt(t,10)}).reduce(function(t,e){return t+e},0):e:parseInt(t[i],10),n=!n;return r%10==0}function Ie(t,e){for(var r=0,n=0;n<t.length;n++)r+=t[n]*(e-n);return r}var Re={andover:["10","12"],atlanta:["60","67"],austin:["50","53"],brookhaven:["01","02","03","04","05","06","11","13","14","16","21","22","23","25","34","51","52","54","55","56","57","58","59","65"],cincinnati:["30","32","35","36","37","38","61"],fresno:["15","24"],internet:["20","26","27","45","46","47"],kansas:["40","44"],memphis:["94","95"],ogden:["80","90"],philadelphia:["33","39","41","42","43","46","48","62","63","64","66","68","71","72","73","74","75","76","77","81","82","83","84","85","86","87","88","91","92","93","98","99"],sba:["31"]};function Le(t){for(var e=!1,r=!1,n=0;n<3;n++)if(!e&&/[AEIOU]/.test(t[n]))e=!0;else if(!r&&e&&"X"===t[n])r=!0;else if(0<n){if(e&&!r&&!/[AEIOU]/.test(t[n]))return;if(r&&!/X/.test(t[n]))return}return 1}var Me={"bg-BG":/^\d{10}$/,"cs-CZ":/^\d{6}\/{0,1}\d{3,4}$/,"de-AT":/^\d{9}$/,"de-DE":/^[1-9]\d{10}$/,"dk-DK":/^\d{6}-{0,1}\d{4}$/,"el-CY":/^[09]\d{7}[A-Z]$/,"el-GR":/^([0-4]|[7-9])\d{8}$/,"en-CA":/^\d{9}$/,"en-GB":/^\d{10}$|^(?!GB|NK|TN|ZZ)(?![DFIQUV])[A-Z](?![DFIQUVO])[A-Z]\d{6}[ABCD ]$/i,"en-IE":/^\d{7}[A-W][A-IW]{0,1}$/i,"en-US":/^\d{2}[- ]{0,1}\d{7}$/,"es-ES":/^(\d{0,8}|[XYZKLM]\d{7})[A-HJ-NP-TV-Z]$/i,"et-EE":/^[1-6]\d{6}(00[1-9]|0[1-9][0-9]|[1-6][0-9]{2}|70[0-9]|710)\d$/,"fi-FI":/^\d{6}[-+A]\d{3}[0-9A-FHJ-NPR-Y]$/i,"fr-BE":/^\d{11}$/,"fr-FR":/^[0-3]\d{12}$|^[0-3]\d\s\d{2}(\s\d{3}){3}$/,"fr-LU":/^\d{13}$/,"hr-HR":/^\d{11}$/,"hu-HU":/^8\d{9}$/,"it-IT":/^[A-Z]{6}[L-NP-V0-9]{2}[A-EHLMPRST][L-NP-V0-9]{2}[A-ILMZ][L-NP-V0-9]{3}[A-Z]$/i,"lv-LV":/^\d{6}-{0,1}\d{5}$/,"mt-MT":/^\d{3,7}[APMGLHBZ]$|^([1-8])\1\d{7}$/i,"nl-NL":/^\d{9}$/,"pl-PL":/^\d{10,11}$/,"pt-BR":/(?:^\d{11}$)|(?:^\d{14}$)/,"pt-PT":/^\d{9}$/,"ro-RO":/^\d{13}$/,"sk-SK":/^\d{6}\/{0,1}\d{3,4}$/,"sl-SI":/^[1-9]\d{7}$/,"sv-SE":/^(\d{6}[-+]{0,1}\d{4}|(18|19|20)\d{6}[-+]{0,1}\d{4})$/};Me["lb-LU"]=Me["fr-LU"],Me["lt-LT"]=Me["et-EE"],Me["nl-BE"]=Me["fr-BE"],Me["fr-CA"]=Me["en-CA"];var Be={"bg-BG":function(t){var e=t.slice(0,2),r=parseInt(t.slice(2,4),10),e=40<r?(r-=40,"20".concat(e)):20<r?(r-=20,"18".concat(e)):"19".concat(e);if(r<10&&(r="0".concat(r)),!st("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),i=[2,4,8,5,10,9,7,3,6],a=0,o=0;o<i.length;o++)a+=n[o]*i[o];return(a=a%11==10?0:a%11)===n[9]},"cs-CZ":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(0,2),10);if(10===t.length)e=(e<54?"20":"19").concat(e);else{if("000"===t.slice(6))return!1;if(!(e<54))return!1;e="19".concat(e)}3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join(""));var r=parseInt(t.slice(2,4),10);if(50<r&&(r-=50),20<r){if(parseInt(e,10)<2004)return!1;r-=20}if(r<10&&(r="0".concat(r)),!st("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;if(10===t.length&&parseInt(t,10)%11!=0){r=parseInt(t.slice(0,9),10)%11;if(!(parseInt(e,10)<1986&&10==r))return!1;if(0!==parseInt(t.slice(9),10))return!1}return!0},"de-AT":Ze,"de-DE":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=[],n=0;n<e.length-1;n++){r.push("");for(var i=0;i<e.length-1;i++)e[n]===e[i]&&(r[n]+=i)}if(2!==(r=r.filter(function(t){return 1<t.length})).length&&3!==r.length)return!1;if(3===r[0].length){for(var a=r[0].split("").map(function(t){return parseInt(t,10)}),o=0,c=0;c<a.length-1;c++)a[c]+1===a[c+1]&&(o+=1);if(2===o)return!1}return ve(t)},"dk-DK":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(4,6),10);switch(t.slice(6,7)){case"0":case"1":case"2":case"3":e="19".concat(e);break;case"4":case"9":e=(e<37?"20":"19").concat(e);break;default:if(e<37)e="20".concat(e);else{if(!(58<e))return!1;e="18".concat(e)}}if(3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join("")),!st("".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),n=0,i=4,a=0;a<9;a++)n+=r[a]*i,1===--i&&(i=7);return 1!=(n%=11)&&(0===n?0===r[9]:r[9]===11-n)},"el-CY":function(t){for(var e=t.slice(0,8).split("").map(function(t){return parseInt(t,10)}),r=0,n=1;n<e.length;n+=2)r+=e[n];for(var i=0;i<e.length;i+=2)e[i]<2?r+=1-e[i]:(r+=2*(e[i]-2)+5,4<e[i]&&(r+=2));return String.fromCharCode(r%26+65)===t.charAt(8)},"el-GR":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=0,n=0;n<8;n++)r+=e[n]*Math.pow(2,8-n);return r%11%10===e[8]},"en-CA":function(t){var e=t.split(""),t=e.filter(function(t,e){return e%2}).map(function(t){return 2*Number(t)}).join("").split("");return e.filter(function(t,e){return!(e%2)}).concat(t).map(function(t){return Number(t)}).reduce(function(t,e){return t+e})%10==0},"en-IE":function(t){var e=Ie(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8);return 9===t.length&&"W"!==t[8]&&(e+=9*(t[8].charCodeAt(0)-64)),0===(e%=23)?"W"===t[7].toUpperCase():t[7].toUpperCase()===String.fromCharCode(64+e)},"en-US":function(t){return-1!==function(){var t,e=[];for(t in Re)Re.hasOwnProperty(t)&&e.push.apply(e,r(Re[t]));return e}().indexOf(t.slice(0,2))},"es-ES":function(t){var e=t.toUpperCase().split("");if(isNaN(parseInt(e[0],10))&&1<e.length){var r=0;switch(e[0]){case"Y":r=1;break;case"Z":r=2}e.splice(0,1,r)}else for(;e.length<9;)e.unshift(0);return e=e.join(""),t=parseInt(e.slice(0,8),10)%23,e[8]===["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][t]},"et-EE":function(t){var e=t.slice(1,3);switch(t.slice(0,1)){case"1":case"2":e="18".concat(e);break;case"3":case"4":e="19".concat(e);break;default:e="20".concat(e)}if(!st("".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7)),"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),n=0,i=1,a=0;a<10;a++)n+=r[a]*i,10===(i+=1)&&(i=1);if(n%11==10){for(var n=0,i=3,o=0;o<10;o++)n+=r[o]*i,10===(i+=1)&&(i=1);if(n%11==10)return 0===r[10]}return n%11===r[10]},"fi-FI":function(t){var e=t.slice(4,6);switch(t.slice(6,7)){case"+":e="18".concat(e);break;case"-":e="19".concat(e);break;default:e="20".concat(e)}if(!st("".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD"))return!1;var r=parseInt(t.slice(0,6)+t.slice(7,10),10)%31;return r<10?r===parseInt(t.slice(10),10):["A","B","C","D","E","F","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y"][r-=10]===t.slice(10)},"fr-BE":function(t){if(("00"!==t.slice(2,4)||"00"!==t.slice(4,6))&&!st("".concat(t.slice(0,2),"/").concat(t.slice(2,4),"/").concat(t.slice(4,6)),"YY/MM/DD"))return!1;var e=97-parseInt(t.slice(0,9),10)%97,r=parseInt(t.slice(9,11),10);return e===r||97-parseInt("2".concat(t.slice(0,9)),10)%97===r},"fr-FR":function(t){return t=t.replace(/\s/g,""),parseInt(t.slice(0,10),10)%511===parseInt(t.slice(10,13),10)},"fr-LU":function(t){return!!st("".concat(t.slice(0,4),"/").concat(t.slice(4,6),"/").concat(t.slice(6,8)),"YYYY/MM/DD")&&(!!Ze(t.slice(0,12))&&function(t){for(var e=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],r=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],n=t.split("").reverse().join(""),i=0,a=0;a<n.length;a++)i=e[i][r[a%8][parseInt(n[a],10)]];return 0===i}("".concat(t.slice(0,11)).concat(t[12])))},"hr-HR":ve,"hu-HU":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=8,n=1;n<9;n++)r+=e[n]*(n+1);return r%11===e[9]},"it-IT":function(t){var e=t.toUpperCase().split("");if(!Le(e.slice(0,3)))return!1;if(!Le(e.slice(3,6)))return!1;for(var r={L:"0",M:"1",N:"2",P:"3",Q:"4",R:"5",S:"6",T:"7",U:"8",V:"9"},n=0,i=[6,7,9,10,12,13,14];n<i.length;n++){var a=i[n];e[a]in r&&e.splice(a,1,r[e[a]])}var o={A:"01",B:"02",C:"03",D:"04",E:"05",H:"06",L:"07",M:"08",P:"09",R:"10",S:"11",T:"12"}[e[8]];if(40<(t=parseInt(e[9]+e[10],10))&&(t-=40),t<10&&(t="0".concat(t)),!st("".concat(e[6]).concat(e[7],"/").concat(o,"/").concat(t),"YY/MM/DD"))return!1;for(var c=0,s=1;s<e.length-1;s+=2){var u=parseInt(e[s],10);c+=u=isNaN(u)?e[s].charCodeAt(0)-65:u}for(var l={A:1,B:0,C:5,D:7,E:9,F:13,G:15,H:17,I:19,J:21,K:2,L:4,M:18,N:20,O:11,P:3,Q:6,R:8,S:12,T:14,U:16,V:10,W:22,X:25,Y:24,Z:23,0:1,1:0},d=0;d<e.length-1;d+=2){var f,A=0;e[d]in l?A=l[e[d]]:(A=2*(f=parseInt(e[d],10))+1,4<f&&(A+=2)),c+=A}return String.fromCharCode(65+c%26)===e[15]},"lv-LV":function(t){var e=(t=t.replace(/\W/,"")).slice(0,2);if("32"===e)return!0;if("00"!==t.slice(2,4)){var r=t.slice(4,6);switch(t[6]){case"0":r="18".concat(r);break;case"1":r="19".concat(r);break;default:r="20".concat(r)}if(!st("".concat(r,"/").concat(t.slice(2,4),"/").concat(e),"YYYY/MM/DD"))return!1}for(var n=1101,i=[1,6,3,7,9,10,5,8,4,2],a=0;a<t.length-1;a++)n-=parseInt(t[a],10)*i[a];return parseInt(t[10],10)===n%11},"mt-MT":function(t){if(9!==t.length){for(var e=t.toUpperCase().split("");e.length<8;)e.unshift(0);switch(t[7]){case"A":case"P":if(0===parseInt(e[6],10))return!1;break;default:var r=parseInt(e.join("").slice(0,5),10);if(32e3<r)return!1;if(r===parseInt(e.join("").slice(5,7),10))return!1}}return!0},"nl-NL":function(t){return Ie(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11===parseInt(t[8],10)},"pl-PL":function(t){if(10===t.length){for(var e=[6,5,7,2,3,4,5,6,7],r=0,n=0;n<e.length;n++)r+=parseInt(t[n],10)*e[n];return 10===(r%=11)?!1:r===parseInt(t[9],10)}var i=t.slice(0,2),a=parseInt(t.slice(2,4),10);if(80<a?(i="18".concat(i),a-=80):60<a?(i="22".concat(i),a-=60):40<a?(i="21".concat(i),a-=40):20<a?(i="20".concat(i),a-=20):i="19".concat(i),a<10&&(a="0".concat(a)),!st("".concat(i,"/").concat(a,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var o=0,c=1,s=0;s<t.length-1;s++)o+=parseInt(t[s],10)*c%10,10<(c+=2)?c=1:5===c&&(c+=2);return(o=10-o%10)===parseInt(t[10],10)},"pt-BR":function(t){if(11===t.length){var e=0;if("11111111111"===t||"22222222222"===t||"33333333333"===t||"44444444444"===t||"55555555555"===t||"66666666666"===t||"77777777777"===t||"88888888888"===t||"99999999999"===t||"00000000000"===t)return!1;for(var r=1;r<=9;r++)e+=parseInt(t.substring(r-1,r),10)*(11-r);if((l=10===(l=10*e%11)?0:l)!==parseInt(t.substring(9,10),10))return!1;e=0;for(var n=1;n<=10;n++)e+=parseInt(t.substring(n-1,n),10)*(12-n);return(l=10===(l=10*e%11)?0:l)!==parseInt(t.substring(10,11),10)?!1:!0}if("00000000000000"===t||"11111111111111"===t||"22222222222222"===t||"33333333333333"===t||"44444444444444"===t||"55555555555555"===t||"66666666666666"===t||"77777777777777"===t||"88888888888888"===t||"99999999999999"===t)return!1;for(var i=t.length-2,a=t.substring(0,i),o=t.substring(i),c=0,s=i-7,u=i;1<=u;u--)c+=a.charAt(i-u)*s,--s<2&&(s=9);var l=c%11<2?0:11-c%11;if(l!==parseInt(o.charAt(0),10))return!1;for(var a=t.substring(0,i+=1),c=0,s=i-7,d=i;1<=d;d--)c+=a.charAt(i-d)*s,--s<2&&(s=9);return(l=c%11<2?0:11-c%11)===parseInt(o.charAt(1),10)},"pt-PT":function(t){var e=11-Ie(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11;return 9<e?0===parseInt(t[8],10):e===parseInt(t[8],10)},"ro-RO":function(t){if("9000"===t.slice(0,4))return!0;var e=t.slice(1,3);switch(t[0]){case"1":case"2":e="19".concat(e);break;case"3":case"4":e="18".concat(e);break;case"5":case"6":e="20".concat(e)}var r="".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7));if(8===r.length){if(!st(r,"YY/MM/DD"))return!1}else if(!st(r,"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),i=[2,7,9,1,4,6,3,5,8,2,7,9],a=0,o=0;o<i.length;o++)a+=n[o]*i[o];return a%11==10?1===n[12]:n[12]===a%11},"sk-SK":function(t){if(9===t.length){if("000"===(t=t.replace(/\W/,"")).slice(6))return!1;if(53<(e=parseInt(t.slice(0,2),10)))return!1;var e=(e<10?"190":"19").concat(e),r=parseInt(t.slice(2,4),10);if(50<r&&(r-=50),r<10&&(r="0".concat(r)),!st("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1}return!0},"sl-SI":function(t){var e=11-Ie(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8)%11;return 10==e?0===parseInt(t[7],10):e===parseInt(t[7],10)},"sv-SE":function(t){var e=t.slice(0),r="",n=(e=11<t.length?e.slice(2):e).slice(2,4),i=parseInt(e.slice(4,6),10);if(11<t.length)r=t.slice(0,4);else if(r=t.slice(0,2),11===t.length&&i<60){var a=(new Date).getFullYear().toString(),e=parseInt(a.slice(0,2),10),a=parseInt(a,10);if("-"===t[6])r=(parseInt("".concat(e).concat(r),10)>a?"".concat(e-1):"".concat(e)).concat(r);else if(r="".concat(e-1).concat(r),a-parseInt(r,10)<100)return!1}if(60<i&&(i-=60),i<10&&(i="0".concat(i)),8===(i="".concat(r,"/").concat(n,"/").concat(i)).length){if(!st(i,"YY/MM/DD"))return!1}else if(!st(i,"YYYY/MM/DD"))return!1;return Ze(t.replace(/\W/,""))}};Be["lb-LU"]=Be["fr-LU"],Be["lt-LT"]=Be["et-EE"],Be["nl-BE"]=Be["fr-BE"],Be["fr-CA"]=Be["en-CA"];var $t=/[-\\\/!@#$%\^&\*\(\)\+\=\[\]]+/g,Ce={"de-AT":$t,"de-DE":/[\/\\]/g,"fr-BE":$t};Ce["nl-BE"]=Ce["fr-BE"];var Ne={"am-AM":/^(\+?374|0)((10|[9|7][0-9])\d{6}$|[2-4]\d{7}$)/,"ar-AE":/^((\+?971)|0)?5[024568]\d{7}$/,"ar-BH":/^(\+?973)?(3|6)\d{7}$/,"ar-DZ":/^(\+?213|0)(5|6|7)\d{8}$/,"ar-LB":/^(\+?961)?((3|81)\d{6}|7\d{7})$/,"ar-EG":/^((\+?20)|0)?1[0125]\d{8}$/,"ar-IQ":/^(\+?964|0)?7[0-9]\d{8}$/,"ar-JO":/^(\+?962|0)?7[789]\d{7}$/,"ar-KW":/^(\+?965)([569]\d{7}|41\d{6})$/,"ar-LY":/^((\+?218)|0)?(9[1-6]\d{7}|[1-8]\d{7,9})$/,"ar-MA":/^(?:(?:\+|00)212|0)[5-7]\d{8}$/,"ar-OM":/^((\+|00)968)?(9[1-9])\d{6}$/,"ar-PS":/^(\+?970|0)5[6|9](\d{7})$/,"ar-SA":/^(!?(\+?966)|0)?5\d{8}$/,"ar-SD":/^((\+?249)|0)?(9[012369]|1[012])\d{7}$/,"ar-SY":/^(!?(\+?963)|0)?9\d{8}$/,"ar-TN":/^(\+?216)?[2459]\d{7}$/,"az-AZ":/^(\+994|0)(10|5[015]|7[07]|99)\d{7}$/,"bs-BA":/^((((\+|00)3876)|06))((([0-3]|[5-6])\d{6})|(4\d{7}))$/,"be-BY":/^(\+?375)?(24|25|29|33|44)\d{7}$/,"bg-BG":/^(\+?359|0)?8[789]\d{7}$/,"bn-BD":/^(\+?880|0)1[13456789][0-9]{8}$/,"ca-AD":/^(\+376)?[346]\d{5}$/,"cs-CZ":/^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"da-DK":/^(\+?45)?\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/,"de-DE":/^((\+49|0)1)(5[0-25-9]\d|6([23]|0\d?)|7([0-57-9]|6\d))\d{7,9}$/,"de-AT":/^(\+43|0)\d{1,4}\d{3,12}$/,"de-CH":/^(\+41|0)([1-9])\d{1,9}$/,"de-LU":/^(\+352)?((6\d1)\d{6})$/,"dv-MV":/^(\+?960)?(7[2-9]|9[1-9])\d{5}$/,"el-GR":/^(\+?30|0)?6(8[5-9]|9(?![26])[0-9])\d{7}$/,"el-CY":/^(\+?357?)?(9(9|6)\d{6})$/,"en-AI":/^(\+?1|0)264(?:2(35|92)|4(?:6[1-2]|76|97)|5(?:3[6-9]|8[1-4])|7(?:2(4|9)|72))\d{4}$/,"en-AU":/^(\+?61|0)4\d{8}$/,"en-AG":/^(?:\+1|1)268(?:464|7(?:1[3-9]|[28]\d|3[0246]|64|7[0-689]))\d{4}$/,"en-BM":/^(\+?1)?441(((3|7)\d{6}$)|(5[0-3][0-9]\d{4}$)|(59\d{5}$))/,"en-BS":/^(\+?1[-\s]?|0)?\(?242\)?[-\s]?\d{3}[-\s]?\d{4}$/,"en-GB":/^(\+?44|0)7\d{9}$/,"en-GG":/^(\+?44|0)1481\d{6}$/,"en-GH":/^(\+233|0)(20|50|24|54|27|57|26|56|23|28|55|59)\d{7}$/,"en-GY":/^(\+592|0)6\d{6}$/,"en-HK":/^(\+?852[-\s]?)?[456789]\d{3}[-\s]?\d{4}$/,"en-MO":/^(\+?853[-\s]?)?[6]\d{3}[-\s]?\d{4}$/,"en-IE":/^(\+?353|0)8[356789]\d{7}$/,"en-IN":/^(\+?91|0)?[6789]\d{9}$/,"en-JM":/^(\+?876)?\d{7}$/,"en-KE":/^(\+?254|0)(7|1)\d{8}$/,"fr-CF":/^(\+?236| ?)(70|75|77|72|21|22)\d{6}$/,"en-SS":/^(\+?211|0)(9[1257])\d{7}$/,"en-KI":/^((\+686|686)?)?( )?((6|7)(2|3|8)[0-9]{6})$/,"en-KN":/^(?:\+1|1)869(?:46\d|48[89]|55[6-8]|66\d|76[02-7])\d{4}$/,"en-LS":/^(\+?266)(22|28|57|58|59|27|52)\d{6}$/,"en-MT":/^(\+?356|0)?(99|79|77|21|27|22|25)[0-9]{6}$/,"en-MU":/^(\+?230|0)?\d{8}$/,"en-NA":/^(\+?264|0)(6|8)\d{7}$/,"en-NG":/^(\+?234|0)?[789]\d{9}$/,"en-NZ":/^(\+?64|0)[28]\d{7,9}$/,"en-PG":/^(\+?675|0)?(7\d|8[18])\d{6}$/,"en-PK":/^((00|\+)?92|0)3[0-6]\d{8}$/,"en-PH":/^(09|\+639)\d{9}$/,"en-RW":/^(\+?250|0)?[7]\d{8}$/,"en-SG":/^(\+65)?[3689]\d{7}$/,"en-SL":/^(\+?232|0)\d{8}$/,"en-TZ":/^(\+?255|0)?[67]\d{8}$/,"en-UG":/^(\+?256|0)?[7]\d{8}$/,"en-US":/^((\+1|1)?( |-)?)?(\([2-9][0-9]{2}\)|[2-9][0-9]{2})( |-)?([2-9][0-9]{2}( |-)?[0-9]{4})$/,"en-ZA":/^(\+?27|0)\d{9}$/,"en-ZM":/^(\+?26)?09[567]\d{7}$/,"en-ZW":/^(\+263)[0-9]{9}$/,"en-BW":/^(\+?267)?(7[1-8]{1})\d{6}$/,"es-AR":/^\+?549(11|[2368]\d)\d{8}$/,"es-BO":/^(\+?591)?(6|7)\d{7}$/,"es-CO":/^(\+?57)?3(0(0|1|2|4|5)|1\d|2[0-4]|5(0|1))\d{7}$/,"es-CL":/^(\+?56|0)[2-9]\d{1}\d{7}$/,"es-CR":/^(\+506)?[2-8]\d{7}$/,"es-CU":/^(\+53|0053)?5\d{7}$/,"es-DO":/^(\+?1)?8[024]9\d{7}$/,"es-HN":/^(\+?504)?[9|8|3|2]\d{7}$/,"es-EC":/^(\+?593|0)([2-7]|9[2-9])\d{7}$/,"es-ES":/^(\+?34)?[6|7]\d{8}$/,"es-PE":/^(\+?51)?9\d{8}$/,"es-MX":/^(\+?52)?(1|01)?\d{10,11}$/,"es-NI":/^(\+?505)\d{7,8}$/,"es-PA":/^(\+?507)\d{7,8}$/,"es-PY":/^(\+?595|0)9[9876]\d{7}$/,"es-SV":/^(\+?503)?[67]\d{7}$/,"es-UY":/^(\+598|0)9[1-9][\d]{6}$/,"es-VE":/^(\+?58)?(2|4)\d{9}$/,"et-EE":/^(\+?372)?\s?(5|8[1-4])\s?([0-9]\s?){6,7}$/,"fa-IR":/^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/,"fi-FI":/^(\+?358|0)\s?(4[0-6]|50)\s?(\d\s?){4,8}$/,"fj-FJ":/^(\+?679)?\s?\d{3}\s?\d{4}$/,"fo-FO":/^(\+?298)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"fr-BF":/^(\+226|0)[67]\d{7}$/,"fr-BJ":/^(\+229)\d{8}$/,"fr-CD":/^(\+?243|0)?(8|9)\d{8}$/,"fr-CM":/^(\+?237)6[0-9]{8}$/,"fr-FR":/^(\+?33|0)[67]\d{8}$/,"fr-GF":/^(\+?594|0|00594)[67]\d{8}$/,"fr-GP":/^(\+?590|0|00590)[67]\d{8}$/,"fr-MQ":/^(\+?596|0|00596)[67]\d{8}$/,"fr-PF":/^(\+?689)?8[789]\d{6}$/,"fr-RE":/^(\+?262|0|00262)[67]\d{8}$/,"fr-WF":/^(\+681)?\d{6}$/,"he-IL":/^(\+972|0)([23489]|5[012345689]|77)[1-9]\d{6}$/,"hu-HU":/^(\+?36|06)(20|30|31|50|70)\d{7}$/,"id-ID":/^(\+?62|0)8(1[123456789]|2[1238]|3[1238]|5[12356789]|7[78]|9[56789]|8[123456789])([\s?|\d]{5,11})$/,"ir-IR":/^(\+98|0)?9\d{9}$/,"it-IT":/^(\+?39)?\s?3\d{2} ?\d{6,7}$/,"it-SM":/^((\+378)|(0549)|(\+390549)|(\+3780549))?6\d{5,9}$/,"ja-JP":/^(\+81[ \-]?(\(0\))?|0)[6789]0[ \-]?\d{4}[ \-]?\d{4}$/,"ka-GE":/^(\+?995)?(79\d{7}|5\d{8})$/,"kk-KZ":/^(\+?7|8)?7\d{9}$/,"kl-GL":/^(\+?299)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"ko-KR":/^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/,"ky-KG":/^(\+?7\s?\+?7|0)\s?\d{2}\s?\d{3}\s?\d{4}$/,"lt-LT":/^(\+370|8)\d{8}$/,"lv-LV":/^(\+?371)2\d{7}$/,"mg-MG":/^((\+?261|0)(2|3)\d)?\d{7}$/,"mn-MN":/^(\+|00|011)?976(77|81|88|91|94|95|96|99)\d{6}$/,"my-MM":/^(\+?959|09|9)(2[5-7]|3[1-2]|4[0-5]|6[6-9]|7[5-9]|9[6-9])[0-9]{7}$/,"ms-MY":/^(\+?60|0)1(([0145](-|\s)?\d{7,8})|([236-9](-|\s)?\d{7}))$/,"mz-MZ":/^(\+?258)?8[234567]\d{7}$/,"nb-NO":/^(\+?47)?[49]\d{7}$/,"ne-NP":/^(\+?977)?9[78]\d{8}$/,"nl-BE":/^(\+?32|0)4\d{8}$/,"nl-NL":/^(((\+|00)?31\(0\))|((\+|00)?31)|0)6{1}\d{8}$/,"nl-AW":/^(\+)?297(56|59|64|73|74|99)\d{5}$/,"nn-NO":/^(\+?47)?[49]\d{7}$/,"pl-PL":/^(\+?48)? ?([5-8]\d|45) ?\d{3} ?\d{2} ?\d{2}$/,"pt-BR":/^((\+?55\ ?[1-9]{2}\ ?)|(\+?55\ ?\([1-9]{2}\)\ ?)|(0[1-9]{2}\ ?)|(\([1-9]{2}\)\ ?)|([1-9]{2}\ ?))((\d{4}\-?\d{4})|(9[1-9]{1}\d{3}\-?\d{4}))$/,"pt-PT":/^(\+?351)?9[1236]\d{7}$/,"pt-AO":/^(\+244)\d{9}$/,"ro-MD":/^(\+?373|0)((6(0|1|2|6|7|8|9))|(7(6|7|8|9)))\d{6}$/,"ro-RO":/^(\+?40|0)\s?7\d{2}(\/|\s|\.|-)?\d{3}(\s|\.|-)?\d{3}$/,"ru-RU":/^(\+?7|8)?9\d{9}$/,"si-LK":/^(?:0|94|\+94)?(7(0|1|2|4|5|6|7|8)( |-)?)\d{7}$/,"sl-SI":/^(\+386\s?|0)(\d{1}\s?\d{3}\s?\d{2}\s?\d{2}|\d{2}\s?\d{3}\s?\d{3})$/,"sk-SK":/^(\+?421)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"so-SO":/^(\+?252|0)((6[0-9])\d{7}|(7[1-9])\d{7})$/,"sq-AL":/^(\+355|0)6[789]\d{6}$/,"sr-RS":/^(\+3816|06)[- \d]{5,9}$/,"sv-SE":/^(\+?46|0)[\s\-]?7[\s\-]?[02369]([\s\-]?\d){7}$/,"tg-TJ":/^(\+?992)?[5][5]\d{7}$/,"th-TH":/^(\+66|66|0)\d{9}$/,"tr-TR":/^(\+?90|0)?5\d{9}$/,"tk-TM":/^(\+993|993|8)\d{8}$/,"uk-UA":/^(\+?38|8)?0\d{9}$/,"uz-UZ":/^(\+?998)?(6[125-79]|7[1-69]|88|9\d)\d{7}$/,"vi-VN":/^((\+?84)|0)((3([2-9]))|(5([25689]))|(7([0|6-9]))|(8([1-9]))|(9([0-9])))([0-9]{7})$/,"zh-CN":/^((\+|00)86)?(1[3-9]|9[28])\d{9}$/,"zh-TW":/^(\+?886\-?|0)?9\d{8}$/,"dz-BT":/^(\+?975|0)?(17|16|77|02)\d{6}$/,"ar-YE":/^(((\+|00)9677|0?7)[0137]\d{7}|((\+|00)967|0)[1-7]\d{6})$/,"ar-EH":/^(\+?212|0)[\s\-]?(5288|5289)[\s\-]?\d{5}$/,"fa-AF":/^(\+93|0)?(2{1}[0-8]{1}|[3-5]{1}[0-4]{1})(\d{7})$/};Ne["en-CA"]=Ne["en-US"],Ne["fr-CA"]=Ne["en-CA"],Ne["fr-BE"]=Ne["nl-BE"],Ne["zh-HK"]=Ne["en-HK"],Ne["zh-MO"]=Ne["en-MO"],Ne["ga-IE"]=Ne["en-IE"],Ne["fr-CH"]=Ne["de-CH"],Ne["it-CH"]=Ne["fr-CH"];var pt=Object.keys(Ne),Fe=/^(0x)[0-9a-f]{40}$/i;var De={symbol:"$",require_symbol:!1,allow_space_after_symbol:!1,symbol_after_digits:!1,allow_negatives:!0,parens_for_negatives:!1,negative_sign_before_digits:!1,negative_sign_after_digits:!1,allow_negative_sign_placeholder:!1,thousands_separator:",",decimal_separator:".",allow_decimal:!0,require_decimal:!1,digits_after_decimal:[2],allow_space_after_digits:!1};var Te=/^(bc1)[a-z0-9]{25,39}$/,Ge=/^(1|3)[A-HJ-NP-Za-km-z1-9]{25,39}$/;var Oe=/^[A-Z]{3}(U[0-9]{7})|([J,Z][0-9]{6,7})$/,Pe=/^[0-9]$/;function He(t){if(u(t),t=t.toUpperCase(),!Oe.test(t))return!1;if(11!==t.length)return!0;for(var e,r=0,n=0;n<t.length-1;n++)Pe.test(t[n])?r+=t[n]*Math.pow(2,n):r+=((e=t.charCodeAt(n)-55)<11?e:11<=e&&e<=20?12+e%11:21<=e&&e<=30?23+e%21:34+e%31)*Math.pow(2,n);var i=r%11;return Number(t[t.length-1])===i}var St=He,be=new Set(["aa","ab","ae","af","ak","am","an","ar","as","av","ay","az","az","ba","be","bg","bh","bi","bm","bn","bo","br","bs","ca","ce","ch","co","cr","cs","cu","cv","cy","da","de","dv","dz","ee","el","en","eo","es","et","eu","fa","ff","fi","fj","fo","fr","fy","ga","gd","gl","gn","gu","gv","ha","he","hi","ho","hr","ht","hu","hy","hz","ia","id","ie","ig","ii","ik","io","is","it","iu","ja","jv","ka","kg","ki","kj","kk","kl","km","kn","ko","kr","ks","ku","kv","kw","ky","la","lb","lg","li","ln","lo","lt","lu","lv","mg","mh","mi","mk","ml","mn","mr","ms","mt","my","na","nb","nd","ne","ng","nl","nn","no","nr","nv","ny","oc","oj","om","or","os","pa","pi","pl","ps","pt","qu","rm","rn","ro","ru","rw","sa","sc","sd","se","sg","si","sk","sl","sm","sn","so","sq","sr","ss","st","su","sv","sw","ta","te","tg","th","ti","tk","tl","tn","to","tr","ts","tt","tw","ty","ug","uk","ur","uz","ve","vi","vo","wa","wo","xh","yi","yo","za","zh","zu"]);var _e=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,Ue=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;var ht=/([01][0-9]|2[0-3])/,gt=/[0-5][0-9]/,Et=new RegExp("[-+]".concat(ht.source,":").concat(gt.source)),$t=new RegExp("([zZ]|".concat(Et.source,")")),Et=new RegExp("".concat(ht.source,":").concat(gt.source,":").concat(/([0-5][0-9]|60)/.source).concat(/(\.[0-9]+)?/.source)),ht=new RegExp("".concat(/[0-9]{4}/.source,"-").concat(/(0[1-9]|1[0-2])/.source,"-").concat(/([12]\d|0[1-9]|3[01])/.source)),gt=new RegExp("".concat(Et.source).concat($t.source)),we=new RegExp("^".concat(ht.source,"[ tT]").concat(gt.source,"$"));var Ke=new Set(["AFG","ALA","ALB","DZA","ASM","AND","AGO","AIA","ATA","ATG","ARG","ARM","ABW","AUS","AUT","AZE","BHS","BHR","BGD","BRB","BLR","BEL","BLZ","BEN","BMU","BTN","BOL","BES","BIH","BWA","BVT","BRA","IOT","BRN","BGR","BFA","BDI","KHM","CMR","CAN","CPV","CYM","CAF","TCD","CHL","CHN","CXR","CCK","COL","COM","COG","COD","COK","CRI","CIV","HRV","CUB","CUW","CYP","CZE","DNK","DJI","DMA","DOM","ECU","EGY","SLV","GNQ","ERI","EST","ETH","FLK","FRO","FJI","FIN","FRA","GUF","PYF","ATF","GAB","GMB","GEO","DEU","GHA","GIB","GRC","GRL","GRD","GLP","GUM","GTM","GGY","GIN","GNB","GUY","HTI","HMD","VAT","HND","HKG","HUN","ISL","IND","IDN","IRN","IRQ","IRL","IMN","ISR","ITA","JAM","JPN","JEY","JOR","KAZ","KEN","KIR","PRK","KOR","KWT","KGZ","LAO","LVA","LBN","LSO","LBR","LBY","LIE","LTU","LUX","MAC","MKD","MDG","MWI","MYS","MDV","MLI","MLT","MHL","MTQ","MRT","MUS","MYT","MEX","FSM","MDA","MCO","MNG","MNE","MSR","MAR","MOZ","MMR","NAM","NRU","NPL","NLD","NCL","NZL","NIC","NER","NGA","NIU","NFK","MNP","NOR","OMN","PAK","PLW","PSE","PAN","PNG","PRY","PER","PHL","PCN","POL","PRT","PRI","QAT","REU","ROU","RUS","RWA","BLM","SHN","KNA","LCA","MAF","SPM","VCT","WSM","SMR","STP","SAU","SEN","SRB","SYC","SLE","SGP","SXM","SVK","SVN","SLB","SOM","ZAF","SGS","SSD","ESP","LKA","SDN","SUR","SJM","SWZ","SWE","CHE","SYR","TWN","TJK","TZA","THA","TLS","TGO","TKL","TON","TTO","TUN","TUR","TKM","TCA","TUV","UGA","UKR","ARE","GBR","USA","UMI","URY","UZB","VUT","VEN","VNM","VGB","VIR","WLF","ESH","YEM","ZMB","ZWE"]);var ye=new Set(["AED","AFN","ALL","AMD","ANG","AOA","ARS","AUD","AWG","AZN","BAM","BBD","BDT","BGN","BHD","BIF","BMD","BND","BOB","BOV","BRL","BSD","BTN","BWP","BYN","BZD","CAD","CDF","CHE","CHF","CHW","CLF","CLP","CNY","COP","COU","CRC","CUC","CUP","CVE","CZK","DJF","DKK","DOP","DZD","EGP","ERN","ETB","EUR","FJD","FKP","GBP","GEL","GHS","GIP","GMD","GNF","GTQ","GYD","HKD","HNL","HRK","HTG","HUF","IDR","ILS","INR","IQD","IRR","ISK","JMD","JOD","JPY","KES","KGS","KHR","KMF","KPW","KRW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LYD","MAD","MDL","MGA","MKD","MMK","MNT","MOP","MRU","MUR","MVR","MWK","MXN","MXV","MYR","MZN","NAD","NGN","NIO","NOK","NPR","NZD","OMR","PAB","PEN","PGK","PHP","PKR","PLN","PYG","QAR","RON","RSD","RUB","RWF","SAR","SBD","SCR","SDG","SEK","SGD","SHP","SLL","SOS","SRD","SSP","STN","SVC","SYP","SZL","THB","TJS","TMT","TND","TOP","TRY","TTD","TWD","TZS","UAH","UGX","USD","USN","UYI","UYU","UYW","UZS","VES","VND","VUV","WST","XAF","XAG","XAU","XBA","XBB","XBC","XBD","XCD","XDR","XOF","XPD","XPF","XPT","XSU","XTS","XUA","XXX","YER","ZAR","ZMW","ZWL"]);var We=/^[A-Z2-7]+=*$/,Ye=/^[A-HJKMNP-TV-Z0-9]+$/,xe={crockford:!1};var ke=/^[A-HJ-NP-Za-km-z1-9]*$/;var Ve=/^[a-z]+\/[a-z0-9\-\+\._]+$/i,ze=/^[a-z\-]+=[a-z0-9\-]+$/i,Xe=/^[a-z0-9!\$&'\(\)\*\+,;=\-\._~:@\/\?%\s]*$/i;var Je=/(?:^magnet:\?|[^?&]&)xt(?:\.1)?=urn:(?:(?:aich|bitprint|btih|ed2k|ed2khash|kzhash|md5|sha1|tree:tiger):[a-z0-9]{32}(?:[a-z0-9]{8})?|btmh:1220[a-z0-9]{64})(?:$|&)/i;function je(t,e){if(u(t),e){e=new RegExp("[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+$"),"g");return t.replace(e,"")}for(var r=t.length-1;/\s/.test(t.charAt(r));)--r;return t.slice(0,r+1)}function qe(t,e){u(t);e=e?new RegExp("^[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+"),"g"):/^\s+/g;return t.replace(e,"")}function Qe(t,e){return je(qe(t,e),e)}var tr=/^(application|audio|font|image|message|model|multipart|text|video)\/[a-zA-Z0-9\.\-\+_]{1,100}$/i,er=/^text\/[a-zA-Z0-9\.\-\+]{1,100};\s?charset=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?$/i,rr=/^multipart\/[a-zA-Z0-9\.\-\+]{1,100}(;\s?(boundary|charset)=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?){0,2}$/i;var nr=/^\(?[+-]?(90(\.0+)?|[1-8]?\d(\.\d+)?)$/,ir=/^\s?[+-]?(180(\.0+)?|1[0-7]\d(\.\d+)?|\d{1,2}(\.\d+)?)\)?$/,ar=/^(([1-8]?\d)\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|90\D+0\D+0)\D+[NSns]?$/i,or=/^\s*([1-7]?\d{1,2}\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|180\D+0\D+0)\D+[EWew]?$/i,cr={checkDMS:!1};var Et=/^\d{3}$/,$t=/^\d{4}$/,ht=/^\d{5}$/,gt=/^\d{6}$/,sr={AD:/^AD\d{3}$/,AT:$t,AU:$t,AZ:/^AZ\d{4}$/,BA:/^([7-8]\d{4}$)/,BE:$t,BG:$t,BR:/^\d{5}-\d{3}$/,BY:/^2[1-4]\d{4}$/,CA:/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][\s\-]?\d[ABCEGHJ-NPRSTV-Z]\d$/i,CH:$t,CN:/^(0[1-7]|1[012356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[1-5]|8[1345]|9[09])\d{4}$/,CZ:/^\d{3}\s?\d{2}$/,DE:ht,DK:$t,DO:ht,DZ:ht,EE:ht,ES:/^(5[0-2]{1}|[0-4]{1}\d{1})\d{3}$/,FI:ht,FR:/^\d{2}\s?\d{3}$/,GB:/^(gir\s?0aa|[a-z]{1,2}\d[\da-z]?\s?(\d[a-z]{2})?)$/i,GR:/^\d{3}\s?\d{2}$/,HR:/^([1-5]\d{4}$)/,HT:/^HT\d{4}$/,HU:$t,ID:ht,IE:/^(?!.*(?:o))[A-Za-z]\d[\dw]\s\w{4}$/i,IL:/^(\d{5}|\d{7})$/,IN:/^((?!10|29|35|54|55|65|66|86|87|88|89)[1-9][0-9]{5})$/,IR:/^(?!(\d)\1{3})[13-9]{4}[1346-9][013-9]{5}$/,IS:Et,IT:ht,JP:/^\d{3}\-\d{4}$/,KE:ht,KR:/^(\d{5}|\d{6})$/,LI:/^(948[5-9]|949[0-7])$/,LT:/^LT\-\d{5}$/,LU:$t,LV:/^LV\-\d{4}$/,LK:ht,MG:Et,MX:ht,MT:/^[A-Za-z]{3}\s{0,1}\d{4}$/,MY:ht,NL:/^\d{4}\s?[a-z]{2}$/i,NO:$t,NP:/^(10|21|22|32|33|34|44|45|56|57)\d{3}$|^(977)$/i,NZ:$t,PL:/^\d{2}\-\d{3}$/,PR:/^00[679]\d{2}([ -]\d{4})?$/,PT:/^\d{4}\-\d{3}?$/,RO:gt,RU:gt,SA:ht,SE:/^[1-9]\d{2}\s?\d{2}$/,SG:gt,SI:$t,SK:/^\d{3}\s?\d{2}$/,TH:ht,TN:$t,TW:/^\d{3}(\d{2})?$/,UA:ht,US:/^\d{5}(-\d{4})?$/,ZA:$t,ZM:ht},ht=Object.keys(sr);function ur(t,e){return u(t),t.replace(new RegExp("[".concat(e,"]+"),"g"),"")}var lr={all_lowercase:!0,gmail_lowercase:!0,gmail_remove_dots:!0,gmail_remove_subaddress:!0,gmail_convert_googlemaildotcom:!0,outlookdotcom_lowercase:!0,outlookdotcom_remove_subaddress:!0,yahoo_lowercase:!0,yahoo_remove_subaddress:!0,yandex_lowercase:!0,icloud_lowercase:!0,icloud_remove_subaddress:!0},dr=["icloud.com","me.com"],fr=["hotmail.at","hotmail.be","hotmail.ca","hotmail.cl","hotmail.co.il","hotmail.co.nz","hotmail.co.th","hotmail.co.uk","hotmail.com","hotmail.com.ar","hotmail.com.au","hotmail.com.br","hotmail.com.gr","hotmail.com.mx","hotmail.com.pe","hotmail.com.tr","hotmail.com.vn","hotmail.cz","hotmail.de","hotmail.dk","hotmail.es","hotmail.fr","hotmail.hu","hotmail.id","hotmail.ie","hotmail.in","hotmail.it","hotmail.jp","hotmail.kr","hotmail.lv","hotmail.my","hotmail.ph","hotmail.pt","hotmail.sa","hotmail.sg","hotmail.sk","live.be","live.co.uk","live.com","live.com.ar","live.com.mx","live.de","live.es","live.eu","live.fr","live.it","live.nl","msn.com","outlook.at","outlook.be","outlook.cl","outlook.co.il","outlook.co.nz","outlook.co.th","outlook.com","outlook.com.ar","outlook.com.au","outlook.com.br","outlook.com.gr","outlook.com.pe","outlook.com.tr","outlook.com.vn","outlook.cz","outlook.de","outlook.dk","outlook.es","outlook.fr","outlook.hu","outlook.id","outlook.ie","outlook.in","outlook.it","outlook.jp","outlook.kr","outlook.lv","outlook.my","outlook.ph","outlook.pt","outlook.sa","outlook.sg","outlook.sk","passport.com"],Ar=["rocketmail.com","yahoo.ca","yahoo.co.uk","yahoo.com","yahoo.de","yahoo.fr","yahoo.in","yahoo.it","ymail.com"],$r=["yandex.ru","yandex.ua","yandex.kz","yandex.com","yandex.by","ya.ru"];function pr(t){return 1<t.length?t:""}var hr=/^[^\s-_](?!.*?[-_]{2,})[a-z0-9-\\][^\s]*[^-_\s]$/;var gr={"cs-CZ":function(t){return/^(([ABCDEFHIJKLMNPRSTUVXYZ]|[0-9])-?){5,8}$/.test(t)},"de-DE":function(t){return/^((A|AA|AB|AC|AE|AH|AK|AM|AN|AÖ|AP|AS|AT|AU|AW|AZ|B|BA|BB|BC|BE|BF|BH|BI|BK|BL|BM|BN|BO|BÖ|BS|BT|BZ|C|CA|CB|CE|CO|CR|CW|D|DA|DD|DE|DH|DI|DL|DM|DN|DO|DU|DW|DZ|E|EA|EB|ED|EE|EF|EG|EH|EI|EL|EM|EN|ER|ES|EU|EW|F|FB|FD|FF|FG|FI|FL|FN|FO|FR|FS|FT|FÜ|FW|FZ|G|GA|GC|GD|GE|GF|GG|GI|GK|GL|GM|GN|GÖ|GP|GR|GS|GT|GÜ|GV|GW|GZ|H|HA|HB|HC|HD|HE|HF|HG|HH|HI|HK|HL|HM|HN|HO|HP|HR|HS|HU|HV|HX|HY|HZ|IK|IL|IN|IZ|J|JE|JL|K|KA|KB|KC|KE|KF|KG|KH|KI|KK|KL|KM|KN|KO|KR|KS|KT|KU|KW|KY|L|LA|LB|LC|LD|LF|LG|LH|LI|LL|LM|LN|LÖ|LP|LR|LU|M|MA|MB|MC|MD|ME|MG|MH|MI|MK|ML|MM|MN|MO|MQ|MR|MS|MÜ|MW|MY|MZ|N|NB|ND|NE|NF|NH|NI|NK|NM|NÖ|NP|NR|NT|NU|NW|NY|NZ|OA|OB|OC|OD|OE|OF|OG|OH|OK|OL|OP|OS|OZ|P|PA|PB|PE|PF|PI|PL|PM|PN|PR|PS|PW|PZ|R|RA|RC|RD|RE|RG|RH|RI|RL|RM|RN|RO|RP|RS|RT|RU|RV|RW|RZ|S|SB|SC|SE|SG|SI|SK|SL|SM|SN|SO|SP|SR|ST|SU|SW|SY|SZ|TE|TF|TG|TO|TP|TR|TS|TT|TÜ|ÜB|UE|UH|UL|UM|UN|V|VB|VG|VK|VR|VS|W|WA|WB|WE|WF|WI|WK|WL|WM|WN|WO|WR|WS|WT|WÜ|WW|WZ|Z|ZE|ZI|ZP|ZR|ZW|ZZ)[- ]?[A-Z]{1,2}[- ]?\d{1,4}|(ABG|ABI|AIB|AIC|ALF|ALZ|ANA|ANG|ANK|APD|ARN|ART|ASL|ASZ|AUR|AZE|BAD|BAR|BBG|BCH|BED|BER|BGD|BGL|BID|BIN|BIR|BIT|BIW|BKS|BLB|BLK|BNA|BOG|BOH|BOR|BOT|BRA|BRB|BRG|BRK|BRL|BRV|BSB|BSK|BTF|BÜD|BUL|BÜR|BÜS|BÜZ|CAS|CHA|CLP|CLZ|COC|COE|CUX|DAH|DAN|DAU|DBR|DEG|DEL|DGF|DIL|DIN|DIZ|DKB|DLG|DON|DUD|DÜW|EBE|EBN|EBS|ECK|EIC|EIL|EIN|EIS|EMD|EMS|ERB|ERH|ERK|ERZ|ESB|ESW|FDB|FDS|FEU|FFB|FKB|FLÖ|FOR|FRG|FRI|FRW|FTL|FÜS|GAN|GAP|GDB|GEL|GEO|GER|GHA|GHC|GLA|GMN|GNT|GOA|GOH|GRA|GRH|GRI|GRM|GRZ|GTH|GUB|GUN|GVM|HAB|HAL|HAM|HAS|HBN|HBS|HCH|HDH|HDL|HEB|HEF|HEI|HER|HET|HGN|HGW|HHM|HIG|HIP|HMÜ|HOG|HOH|HOL|HOM|HOR|HÖS|HOT|HRO|HSK|HST|HVL|HWI|IGB|ILL|JÜL|KEH|KEL|KEM|KIB|KLE|KLZ|KÖN|KÖT|KÖZ|KRU|KÜN|KUS|KYF|LAN|LAU|LBS|LBZ|LDK|LDS|LEO|LER|LEV|LIB|LIF|LIP|LÖB|LOS|LRO|LSZ|LÜN|LUP|LWL|MAB|MAI|MAK|MAL|MED|MEG|MEI|MEK|MEL|MER|MET|MGH|MGN|MHL|MIL|MKK|MOD|MOL|MON|MOS|MSE|MSH|MSP|MST|MTK|MTL|MÜB|MÜR|MYK|MZG|NAB|NAI|NAU|NDH|NEA|NEB|NEC|NEN|NES|NEW|NMB|NMS|NOH|NOL|NOM|NOR|NVP|NWM|OAL|OBB|OBG|OCH|OHA|ÖHR|OHV|OHZ|OPR|OSL|OVI|OVL|OVP|PAF|PAN|PAR|PCH|PEG|PIR|PLÖ|PRÜ|QFT|QLB|RDG|REG|REH|REI|RID|RIE|ROD|ROF|ROK|ROL|ROS|ROT|ROW|RSL|RÜD|RÜG|SAB|SAD|SAN|SAW|SBG|SBK|SCZ|SDH|SDL|SDT|SEB|SEE|SEF|SEL|SFB|SFT|SGH|SHA|SHG|SHK|SHL|SIG|SIM|SLE|SLF|SLK|SLN|SLS|SLÜ|SLZ|SMÜ|SOB|SOG|SOK|SÖM|SON|SPB|SPN|SRB|SRO|STA|STB|STD|STE|STL|SUL|SÜW|SWA|SZB|TBB|TDO|TET|TIR|TÖL|TUT|UEM|UER|UFF|USI|VAI|VEC|VER|VIB|VIE|VIT|VOH|WAF|WAK|WAN|WAR|WAT|WBS|WDA|WEL|WEN|WER|WES|WHV|WIL|WIS|WIT|WIZ|WLG|WMS|WND|WOB|WOH|WOL|WOR|WOS|WRN|WSF|WST|WSW|WTL|WTM|WUG|WÜM|WUN|WUR|WZL|ZEL|ZIG)[- ]?(([A-Z][- ]?\d{1,4})|([A-Z]{2}[- ]?\d{1,3})))[- ]?(E|H)?$/.test(t)},"de-LI":function(t){return/^FL[- ]?\d{1,5}[UZ]?$/.test(t)},"en-IN":function(t){return/^[A-Z]{2}[ -]?[0-9]{1,2}(?:[ -]?[A-Z])(?:[ -]?[A-Z]*)?[ -]?[0-9]{4}$/.test(t)},"es-AR":function(t){return/^(([A-Z]{2} ?[0-9]{3} ?[A-Z]{2})|([A-Z]{3} ?[0-9]{3}))$/.test(t)},"fi-FI":function(t){return/^(?=.{4,7})(([A-Z]{1,3}|[0-9]{1,3})[\s-]?([A-Z]{1,3}|[0-9]{1,5}))$/.test(t)},"hu-HU":function(t){return/^((((?!AAA)(([A-NPRSTVZWXY]{1})([A-PR-Z]{1})([A-HJ-NPR-Z]))|(A[ABC]I)|A[ABC]O|A[A-W]Q|BPI|BPO|UCO|UDO|XAO)-(?!000)\d{3})|(M\d{6})|((CK|DT|CD|HC|H[ABEFIKLMNPRSTVX]|MA|OT|R[A-Z]) \d{2}-\d{2})|(CD \d{3}-\d{3})|(C-(C|X) \d{4})|(X-(A|B|C) \d{4})|(([EPVZ]-\d{5}))|(S A[A-Z]{2} \d{2})|(SP \d{2}-\d{2}))$/.test(t)},"pt-BR":function(t){return/^[A-Z]{3}[ -]?[0-9][A-Z][0-9]{2}|[A-Z]{3}[ -]?[0-9]{4}$/.test(t)},"pt-PT":function(t){return/^([A-Z]{2}|[0-9]{2})[ -·]?([A-Z]{2}|[0-9]{2})[ -·]?([A-Z]{2}|[0-9]{2})$/.test(t)},"sq-AL":function(t){return/^[A-Z]{2}[- ]?((\d{3}[- ]?(([A-Z]{2})|T))|(R[- ]?\d{3}))$/.test(t)},"sv-SE":function(t){return/^[A-HJ-PR-UW-Z]{3} ?[\d]{2}[A-HJ-PR-UW-Z1-9]$|(^[A-ZÅÄÖ ]{2,7}$)/.test(t.trim())}};var Sr=/^[A-Z]$/,Er=/^[a-z]$/,mr=/^[0-9]$/,vr=/^[-#!$@£%^&*()_+|~=`{}\[\]:";'<>?,.\/ ]$/,Zr={minLength:8,minLowercase:1,minUppercase:1,minNumbers:1,minSymbols:1,returnScore:!1,pointsPerUnique:1,pointsPerRepeat:.5,pointsForContainingLower:10,pointsForContainingUpper:10,pointsForContainingNumber:10,pointsForContainingSymbol:10};function Ir(t){var e,r,n=(e=t,r={},Array.from(e).forEach(function(t){r[t]?r[t]+=1:r[t]=1}),r),i={length:t.length,uniqueChars:Object.keys(n).length,uppercaseCount:0,lowercaseCount:0,numberCount:0,symbolCount:0};return Object.keys(n).forEach(function(t){Sr.test(t)?i.uppercaseCount+=n[t]:Er.test(t)?i.lowercaseCount+=n[t]:mr.test(t)?i.numberCount+=n[t]:vr.test(t)&&(i.symbolCount+=n[t])}),i}var Rr={AT:function(t){return/^(AT)?U\d{8}$/.test(t)},BE:function(t){return/^(BE)?\d{10}$/.test(t)},BG:function(t){return/^(BG)?\d{9,10}$/.test(t)},HR:function(t){return/^(HR)?\d{11}$/.test(t)},CY:function(t){return/^(CY)?\w{9}$/.test(t)},CZ:function(t){return/^(CZ)?\d{8,10}$/.test(t)},DK:function(t){return/^(DK)?\d{8}$/.test(t)},EE:function(t){return/^(EE)?\d{9}$/.test(t)},FI:function(t){return/^(FI)?\d{8}$/.test(t)},FR:function(t){return/^(FR)?\w{2}\d{9}$/.test(t)},DE:function(t){return/^(DE)?\d{9}$/.test(t)},EL:function(t){return/^(EL)?\d{9}$/.test(t)},HU:function(t){return/^(HU)?\d{8}$/.test(t)},IE:function(t){return/^(IE)?\d{7}\w{1}(W)?$/.test(t)},IT:function(t){return/^(IT)?\d{11}$/.test(t)},LV:function(t){return/^(LV)?\d{11}$/.test(t)},LT:function(t){return/^(LT)?\d{9,12}$/.test(t)},LU:function(t){return/^(LU)?\d{8}$/.test(t)},MT:function(t){return/^(MT)?\d{8}$/.test(t)},NL:function(t){return/^(NL)?\d{9}B\d{2}$/.test(t)},PL:function(t){return/^(PL)?(\d{10}|(\d{3}-\d{3}-\d{2}-\d{2})|(\d{3}-\d{2}-\d{2}-\d{3}))$/.test(t)},PT:function(t){var e=t.match(/^(PT)?(\d{9})$/);if(!e)return!1;t=e[2],e=11-Ie(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11;return 9<e?0===parseInt(t[8],10):e===parseInt(t[8],10)},RO:function(t){return/^(RO)?\d{2,10}$/.test(t)},SK:function(t){return/^(SK)?\d{10}$/.test(t)},SI:function(t){return/^(SI)?\d{8}$/.test(t)},ES:function(t){return/^(ES)?\w\d{7}[A-Z]$/.test(t)},SE:function(t){return/^(SE)?\d{12}$/.test(t)},AL:function(t){return/^(AL)?\w{9}[A-Z]$/.test(t)},MK:function(t){return/^(MK)?\d{13}$/.test(t)},AU:function(t){return/^(AU)?\d{11}$/.test(t)},BY:function(t){return/^(УНП )?\d{9}$/.test(t)},CA:function(t){return/^(CA)?\d{9}$/.test(t)},IS:function(t){return/^(IS)?\d{5,6}$/.test(t)},IN:function(t){return/^(IN)?\d{15}$/.test(t)},ID:function(t){return/^(ID)?(\d{15}|(\d{2}.\d{3}.\d{3}.\d{1}-\d{3}.\d{3}))$/.test(t)},IL:function(t){return/^(IL)?\d{9}$/.test(t)},KZ:function(t){return/^(KZ)?\d{9}$/.test(t)},NZ:function(t){return/^(NZ)?\d{9}$/.test(t)},NG:function(t){return/^(NG)?(\d{12}|(\d{8}-\d{4}))$/.test(t)},NO:function(t){return/^(NO)?\d{9}MVA$/.test(t)},PH:function(t){return/^(PH)?(\d{12}|\d{3} \d{3} \d{3} \d{3})$/.test(t)},RU:function(t){return/^(RU)?(\d{10}|\d{12})$/.test(t)},SM:function(t){return/^(SM)?\d{5}$/.test(t)},SA:function(t){return/^(SA)?\d{15}$/.test(t)},RS:function(t){return/^(RS)?\d{9}$/.test(t)},CH:function(t){var e,r,n;return/^(CHE[- ]?)?(\d{9}|(\d{3}\.\d{3}\.\d{3})|(\d{3} \d{3} \d{3})) ?(TVA|MWST|IVA)?$/.test(t)&&(e=t.match(/\d/g).map(function(t){return+t}),r=e.pop(),n=[5,4,3,2,7,6,5,4],r===(11-e.reduce(function(t,e,r){return t+e*n[r]},0)%11)%11)},TR:function(t){return/^(TR)?\d{10}$/.test(t)},UA:function(t){return/^(UA)?\d{12}$/.test(t)},GB:function(t){return/^GB((\d{3} \d{4} ([0-8][0-9]|9[0-6]))|(\d{9} \d{3})|(((GD[0-4])|(HA[5-9]))[0-9]{2}))$/.test(t)},UZ:function(t){return/^(UZ)?\d{9}$/.test(t)},AR:function(t){return/^(AR)?\d{11}$/.test(t)},BO:function(t){return/^(BO)?\d{7}$/.test(t)},BR:function(t){return/^(BR)?((\d{2}.\d{3}.\d{3}\/\d{4}-\d{2})|(\d{3}.\d{3}.\d{3}-\d{2}))$/.test(t)},CL:function(t){return/^(CL)?\d{8}-\d{1}$/.test(t)},CO:function(t){return/^(CO)?\d{10}$/.test(t)},CR:function(t){return/^(CR)?\d{9,12}$/.test(t)},EC:function(t){return/^(EC)?\d{13}$/.test(t)},SV:function(t){return/^(SV)?\d{4}-\d{6}-\d{3}-\d{1}$/.test(t)},GT:function(t){return/^(GT)?\d{7}-\d{1}$/.test(t)},HN:function(t){return/^(HN)?$/.test(t)},MX:function(t){return/^(MX)?\w{3,4}\d{6}\w{3}$/.test(t)},NI:function(t){return/^(NI)?\d{3}-\d{6}-\d{4}\w{1}$/.test(t)},PA:function(t){return/^(PA)?$/.test(t)},PY:function(t){return/^(PY)?\d{6,8}-\d{1}$/.test(t)},PE:function(t){return/^(PE)?\d{11}$/.test(t)},DO:function(t){return/^(DO)?(\d{11}|(\d{3}-\d{7}-\d{1})|[1,4,5]{1}\d{8}|([1,4,5]{1})-\d{2}-\d{5}-\d{1})$/.test(t)},UY:function(t){return/^(UY)?\d{12}$/.test(t)},VE:function(t){return/^(VE)?[J,G,V,E]{1}-(\d{9}|(\d{8}-\d{1}))$/.test(t)}};return{version:"13.11.0",toDate:i,toFloat:N,toInt:function(t,e){return u(t),parseInt(t,e||10)},toBoolean:function(t,e){return u(t),e?"1"===t||/^true$/i.test(t):"0"!==t&&!/^false$/i.test(t)&&""!==t},equals:function(t,e){return u(t),t===e},contains:function(t,e,r){return u(t),(r=D(r,T)).ignoreCase?t.toLowerCase().split(F(e).toLowerCase()).length>r.minOccurrences:t.split(F(e)).length>r.minOccurrences},matches:function(t,e,r){return u(t),"[object RegExp]"!==Object.prototype.toString.call(e)&&(e=new RegExp(e,r)),!!t.match(e)},isEmail:J,isURL:function(t,e){if(u(t),!t||/[\s<>]/.test(t))return!1;if(0===t.indexOf("mailto:"))return!1;if((e=D(e,j)).validate_length&&2083<=t.length)return!1;if(!e.allow_fragments&&t.includes("#"))return!1;if(!e.allow_query_components&&(t.includes("?")||t.includes("&")))return!1;var r,n,i=t.split("#");if(1<(i=(t=(i=(t=i.shift()).split("?")).shift()).split("://")).length){if(r=i.shift().toLowerCase(),e.require_valid_protocol&&-1===e.protocols.indexOf(r))return!1}else{if(e.require_protocol)return!1;if("//"===t.slice(0,2)){if(!e.allow_protocol_relative_urls)return!1;i[0]=t.slice(2)}}if(""===(t=i.join("://")))return!1;if(""===(t=(i=t.split("/")).shift())&&!e.require_host)return!0;if(1<(i=t.split("@")).length){if(e.disallow_auth)return!1;if(""===i[0])return!1;if(0<=(s=i.shift()).indexOf(":")&&2<s.split(":").length)return!1;var a=l(s.split(":"),2),o=a[0],c=a[1];if(""===o&&""===c)return!1}var s=null,a=null;if((c=(o=i.join("@")).match(q))?(n="",a=c[1],s=c[2]||null):(n=(i=o.split(":")).shift(),i.length&&(s=i.join(":"))),null!==s&&0<s.length){if(i=parseInt(s,10),!/^[0-9]+$/.test(s)||i<=0||65535<i)return!1}else if(e.require_port)return!1;return e.host_whitelist?Q(n,e.host_whitelist):""===n&&!e.require_host||!!(K(n)||P(n,e)||a&&K(a,6))&&(n=n||a,!e.host_blacklist||!Q(n,e.host_blacklist))},isMACAddress:function t(e,r){return u(e),null!=r&&r.eui&&(r.eui=String(r.eui)),null!=r&&r.no_colons||null!=r&&r.no_separators?"48"===r.eui?et.test(e):"64"!==r.eui&&et.test(e)||it.test(e):"48"===(null==r?void 0:r.eui)?tt.test(e)||rt.test(e):"64"===(null==r?void 0:r.eui)?nt.test(e)||at.test(e):t(e,{eui:"48"})||t(e,{eui:"64"})},isIP:K,isIPRange:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";u(t);var r=t.split("/");if(2!==r.length)return!1;if(!ot.test(r[1]))return!1;if(1<r[1].length&&r[1].startsWith("0"))return!1;if(!K(r[0],e))return!1;var n=null;switch(String(e)){case"4":n=32;break;case"6":n=128;break;default:n=K(r[0],"6")?128:32}return r[1]<=n&&0<=r[1]},isFQDN:P,isBoolean:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:dt;return u(t),e.loose?At.includes(t.toLowerCase()):ft.includes(t)},isIBAN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return u(t),Jt(t,e)&&1===((t=(t=t).replace(/[^A-Z0-9]+/gi,"").toUpperCase()).slice(4)+t.slice(0,4)).replace(/[A-Z]/g,function(t){return t.charCodeAt(0)-55}).match(/\d{1,7}/g).reduce(function(t,e){return Number(t+e)%97},"")},isBIC:function(t){u(t);var e=t.slice(4,6).toUpperCase();return!(!qt.has(e)&&"XK"!==e)&&Qt.test(t)},isAlpha:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(u(t),r=r.ignore)if(r instanceof RegExp)t=t.replace(r,"");else{if("string"!=typeof r)throw new Error("ignore should be instance of a String or RegExp");t=t.replace(new RegExp("[".concat(r.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in o)return o[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphaLocales:H,isAlphanumeric:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(u(t),r=r.ignore)if(r instanceof RegExp)t=t.replace(r,"");else{if("string"!=typeof r)throw new Error("ignore should be instance of a String or RegExp");t=t.replace(new RegExp("[".concat(r.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in s)return s[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphanumericLocales:b,isNumeric:function(t,e){return u(t),(e&&e.no_symbols?vt:new RegExp("^[+-]?([0-9]*[".concat((e||{}).locale?f[e.locale]:".","])?[0-9]+$"))).test(t)},isPassportNumber:function(t,e){return u(t),t=t.replace(/\s/g,"").toUpperCase(),e.toUpperCase()in Zt&&Zt[e].test(t)},isPort:function(t){return Lt(t,{min:0,max:65535})},isLowercase:function(t){return u(t),t===t.toLowerCase()},isUppercase:function(t){return u(t),t===t.toUpperCase()},isAscii:function(t){return u(t),Ct.test(t)},isFullWidth:function(t){return u(t),Nt.test(t)},isHalfWidth:function(t){return u(t),Ft.test(t)},isVariableWidth:function(t){return u(t),Nt.test(t)&&Ft.test(t)},isMultibyte:function(t){return u(t),Dt.test(t)},isSemVer:function(t){return u(t),Ot.test(t)},isSurrogatePair:function(t){return u(t),Pt.test(t)},isInt:Lt,isIMEI:function(t,e){if(u(t),!((e=e||{}).allow_hyphens?Bt:Mt).test(t))return!1;t=t.replace(/-/g,"");for(var r=0,n=2,i=0;i<14;i++){var a=t.substring(14-i-1,14-i),a=parseInt(a,10)*n;r+=10<=a?a%10+1:a,1===n?n+=1:--n}return(10-r%10)%10===parseInt(t.substring(14,15),10)},isFloat:B,isFloatLocales:C,isDecimal:function(t,e){if(u(t),(e=D(e,Ht)).locale in f)return r=bt,n=t.replace(/ /g,""),!r.some(function(t){return n===t})&&(r=e,new RegExp("^[-+]?([0-9]+)?(\\".concat(f[r.locale],"[0-9]{").concat(r.decimal_digits,"})").concat(r.force_decimal?"":"?","$")).test(t));var r,n;throw new Error("Invalid locale '".concat(e.locale,"'"))},isHexadecimal:Ut,isOctal:function(t){return u(t),wt.test(t)},isDivisibleBy:function(t,e){return u(t),N(t)%parseInt(e,10)==0},isHexColor:function(t){return u(t),Kt.test(t)},isRgbColor:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];return u(t),e?yt.test(t)||Wt.test(t)||Yt.test(t)||xt.test(t):yt.test(t)||Wt.test(t)},isHSL:function(t){return u(t),(-1!==(t=t.replace(/\s+/g," ").replace(/\s?(hsla?\(|\)|,)\s?/gi,"$1")).indexOf(",")?kt:Vt).test(t)},isISRC:function(t){return u(t),zt.test(t)},isMD5:function(t){return u(t),te.test(t)},isHash:function(t,e){return u(t),new RegExp("^[a-fA-F0-9]{".concat(ee[e],"}$")).test(t)},isJWT:function(t){return u(t),3===(t=t.split(".")).length&&t.reduce(function(t,e){return t&&ae(e,{urlSafe:!0})},!0)},isJSON:function(t,e){u(t);try{e=D(e,oe);var r=[];e.allow_primitives&&(r=[null,!1,!0]);var n=JSON.parse(t);return r.includes(n)||!!n&&"object"===a(n)}catch(t){}return!1},isEmpty:function(t,e){return u(t),0===((e=D(e,ce)).ignore_whitespace?t.trim():t).length},isLength:function(t,e){var r;u(t);var n="object"===a(e)?(r=e.min||0,e.max):(r=e||0,arguments[2]),i=t.match(/(\uFE0F|\uFE0E)/g)||[],e=t.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g)||[],e=t.length-i.length-e.length;return r<=e&&(void 0===n||e<=n)},isLocale:function(t){return u(t),mt.test(t)},isByteLength:G,isUUID:function(t,e){return u(t),!!(e=se[[void 0,null].includes(e)?"all":e])&&e.test(t)},isMongoId:function(t){return u(t),Ut(t)&&24===t.length},isAfter:function(t,e){return e=i((null==e?void 0:e.comparisonDate)||e||Date().toString()),!!((t=i(t))&&e&&e<t)},isBefore:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:String(new Date);return u(t),e=i(e),!!((t=i(t))&&e&&t<e)},isIn:function(t,e){if(u(t),"[object Array]"!==Object.prototype.toString.call(e))return"object"===a(e)?e.hasOwnProperty(t):!(!e||"function"!=typeof e.indexOf)&&0<=e.indexOf(t);var r,n=[];for(r in e)!{}.hasOwnProperty.call(e,r)||(n[r]=F(e[r]));return 0<=n.indexOf(t)},isLuhnNumber:ue,isCreditCard:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};u(t);var e=e.provider,r=t.replace(/[- ]+/g,"");if(e&&e.toLowerCase()in le){if(!le[e.toLowerCase()].test(r))return!1}else{if(e&&!(e.toLowerCase()in le))throw new Error("".concat(e," is not a valid credit card provider."));if(!de.some(function(t){return t.test(r)}))return!1}return ue(t)},isIdentityCard:function(t,e){if(u(t),e in fe)return fe[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in fe)if(fe.hasOwnProperty(r))if((0,fe[r])(t))return!0;return!1},isEAN:function(t){u(t);var e=Number(t.slice(-1));return pe.test(t)&&e===he(t)},isISIN:function(t){if(u(t),!ge.test(t))return!1;for(var e=!0,r=0,n=t.length-2;0<=n;n--)if("A"<=t[n]&&t[n]<="Z")for(var i=t[n].charCodeAt(0)-55,a=0,o=[i%10,Math.trunc(i/10)];a<o.length;a++){var c=o[a];r+=e?5<=c?1+2*(c-5):2*c:c,e=!e}else{i=t[n].charCodeAt(0)-"0".charCodeAt(0);r+=e?5<=i?1+2*(i-5):2*i:i,e=!e}var s=10*Math.trunc((r+9)/10)-r;return+t[t.length-1]==s},isISBN:function t(e,r){u(e);var n=String((null==r?void 0:r.version)||r);if(!(null!=r&&r.version||r))return t(e,{version:10})||t(e,{version:13});var i=e.replace(/[\s-]+/g,""),a=0;if("10"===n){if(!Se.test(i))return!1;for(var o=0;o<n-1;o++)a+=(o+1)*i.charAt(o);if("X"===i.charAt(9)?a+=100:a+=10*i.charAt(9),a%11==0)return!0}else if("13"===n){if(!Ee.test(i))return!1;for(var c=0;c<12;c++)a+=me[c%2]*i.charAt(c);if(i.charAt(12)-(10-a%10)%10==0)return!0}return!1},isISSN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};u(t);var r="^\\d{4}-?\\d{3}[\\dX]$",r=e.require_hyphen?r.replace("?",""):r;if(!(r=e.case_sensitive?new RegExp(r):new RegExp(r,"i")).test(t))return!1;for(var n=t.replace("-","").toUpperCase(),i=0,a=0;a<n.length;a++){var o=n[a];i+=("X"===o?10:+o)*(8-a)}return i%11==0},isMobilePhone:function(e,t,r){if(u(e),r&&r.strictMode&&!e.startsWith("+"))return!1;if(Array.isArray(t))return t.some(function(t){if(Ne.hasOwnProperty(t)&&Ne[t].test(e))return!0;return!1});if(t in Ne)return Ne[t].test(e);if(t&&"any"!==t)throw new Error("Invalid locale '".concat(t,"'"));for(var n in Ne)if(Ne.hasOwnProperty(n))if(Ne[n].test(e))return!0;return!1},isMobilePhoneLocales:pt,isPostalCode:function(t,e){if(u(t),e in sr)return sr[e].test(t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in sr)if(sr.hasOwnProperty(r))if(sr[r].test(t))return!0;return!1},isPostalCodeLocales:ht,isEthereumAddress:function(t){return u(t),Fe.test(t)},isCurrency:function(t,e){return u(t),function(t){var r="\\d{".concat(t.digits_after_decimal[0],"}");t.digits_after_decimal.forEach(function(t,e){0!==e&&(r="".concat(r,"|\\d{").concat(t,"}"))});var e="(".concat(t.symbol.replace(/\W/,function(t){return"\\".concat(t)}),")").concat(t.require_symbol?"":"?"),n="[1-9]\\d{0,2}(\\".concat(t.thousands_separator,"\\d{3})*"),i="(".concat(["0","[1-9]\\d*",n].join("|"),")?"),n="(\\".concat(t.decimal_separator,"(").concat(r,"))").concat(t.require_decimal?"":"?"),n=i+(t.allow_decimal||t.require_decimal?n:"");return t.allow_negatives&&!t.parens_for_negatives&&(t.negative_sign_after_digits?n+="-?":t.negative_sign_before_digits&&(n="-?"+n)),t.allow_negative_sign_placeholder?n="( (?!\\-))?".concat(n):t.allow_space_after_symbol?n=" ?".concat(n):t.allow_space_after_digits&&(n+="( (?!$))?"),t.symbol_after_digits?n+=e:n=e+n,t.allow_negatives&&(t.parens_for_negatives?n="(\\(".concat(n,"\\)|").concat(n,")"):t.negative_sign_before_digits||t.negative_sign_after_digits||(n="-?"+n)),new RegExp("^(?!-? )(?=.*\\d)".concat(n,"$"))}(e=D(e,De)).test(t)},isBtcAddress:function(t){return u(t),Te.test(t)||Ge.test(t)},isISO6346:He,isFreightContainerID:St,isISO6391:function(t){return u(t),be.has(t)},isISO8601:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};u(t);var r=(e.strictSeparator?Ue:_e).test(t);return r&&e.strict?function(t){var e=t.match(/^(\d{4})-?(\d{3})([ T]{1}\.*|$)/);if(e){var r=Number(e[1]),n=Number(e[2]);return r%4==0&&r%100!=0||r%400==0?n<=366:n<=365}var i=t.match(/(\d{4})-?(\d{0,2})-?(\d*)/).map(Number),e=i[1],r=i[2],n=i[3],t=r&&"0".concat(r).slice(-2),i=n&&"0".concat(n).slice(-2),i=new Date("".concat(e,"-").concat(t||"01","-").concat(i||"01"));return!r||!n||i.getUTCFullYear()===e&&i.getUTCMonth()+1===r&&i.getUTCDate()===n}(t):r},isRFC3339:function(t){return u(t),we.test(t)},isISO31661Alpha2:function(t){return u(t),jt.has(t.toUpperCase())},isISO31661Alpha3:function(t){return u(t),Ke.has(t.toUpperCase())},isISO4217:function(t){return u(t),ye.has(t.toUpperCase())},isBase32:function(t,e){return u(t),(e=D(e,xe)).crockford?Ye.test(t):!(t.length%8!=0||!We.test(t))},isBase58:function(t){return u(t),!!ke.test(t)},isBase64:ae,isDataURI:function(t){u(t);var e=t.split(",");if(e.length<2)return!1;var r=e.shift().trim().split(";");if("data:"!==(t=r.shift()).slice(0,5))return!1;if(""!==(t=t.slice(5))&&!Ve.test(t))return!1;for(var n=0;n<r.length;n++)if((n!==r.length-1||"base64"!==r[n].toLowerCase())&&!ze.test(r[n]))return!1;for(var i=0;i<e.length;i++)if(!Xe.test(e[i]))return!1;return!0},isMagnetURI:function(t){return u(t),0===t.indexOf("magnet:?")&&Je.test(t)},isMailtoURI:function(t,e){if(u(t),0!==t.indexOf("mailto:"))return!1;var t=void 0===(t=(r=l(t.replace("mailto:","").split("?"),2))[0])?"":t,r=void 0===(r=r[1])?"":r;return!t&&!r||!!(r=function(t){var e=new Set(["subject","body","cc","bcc"]),r={cc:"",bcc:""},n=!1;if(4<(t=t.split("&")).length)return!1;var i,a=d(t);try{for(a.s();!(i=a.n()).done;){var o=l(i.value.split("="),2),c=o[0],s=o[1];if(c&&!e.has(c)){n=!0;break}!s||"cc"!==c&&"bcc"!==c||(r[c]=s),c&&e.delete(c)}}catch(t){a.e(t)}finally{a.f()}return!n&&r}(r))&&"".concat(t,",").concat(r.cc,",").concat(r.bcc).split(",").every(function(t){return!(t=Qe(t," "))||J(t,e)})},isMimeType:function(t){return u(t),tr.test(t)||er.test(t)||rr.test(t)},isLatLong:function(t,e){return u(t),e=D(e,cr),!!t.includes(",")&&(!((t=t.split(","))[0].startsWith("(")&&!t[1].endsWith(")")||t[1].endsWith(")")&&!t[0].startsWith("("))&&(e.checkDMS?ar.test(t[0])&&or.test(t[1]):nr.test(t[0])&&ir.test(t[1])))},ltrim:qe,rtrim:je,trim:Qe,escape:function(t){return u(t),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\//g,"&#x2F;").replace(/\\/g,"&#x5C;").replace(/`/g,"&#96;")},unescape:function(t){return u(t),t.replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#x2F;/g,"/").replace(/&#x5C;/g,"\\").replace(/&#96;/g,"`").replace(/&amp;/g,"&")},stripLow:function(t,e){return u(t),ur(t,e?"\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F":"\\x00-\\x1F\\x7F")},whitelist:function(t,e){return u(t),t.replace(new RegExp("[^".concat(e,"]+"),"g"),"")},blacklist:ur,isWhitelisted:function(t,e){u(t);for(var r=t.length-1;0<=r;r--)if(-1===e.indexOf(t[r]))return!1;return!0},normalizeEmail:function(t,e){e=D(e,lr);var r=t.split("@"),t=r.pop();if((r=[r.join("@"),t])[1]=r[1].toLowerCase(),"gmail.com"===r[1]||"googlemail.com"===r[1]){if(e.gmail_remove_subaddress&&(r[0]=r[0].split("+")[0]),e.gmail_remove_dots&&(r[0]=r[0].replace(/\.+/g,pr)),!r[0].length)return!1;(e.all_lowercase||e.gmail_lowercase)&&(r[0]=r[0].toLowerCase()),r[1]=e.gmail_convert_googlemaildotcom?"gmail.com":r[1]}else if(0<=dr.indexOf(r[1])){if(e.icloud_remove_subaddress&&(r[0]=r[0].split("+")[0]),!r[0].length)return!1;(e.all_lowercase||e.icloud_lowercase)&&(r[0]=r[0].toLowerCase())}else if(0<=fr.indexOf(r[1])){if(e.outlookdotcom_remove_subaddress&&(r[0]=r[0].split("+")[0]),!r[0].length)return!1;(e.all_lowercase||e.outlookdotcom_lowercase)&&(r[0]=r[0].toLowerCase())}else if(0<=Ar.indexOf(r[1])){if(e.yahoo_remove_subaddress&&(t=r[0].split("-"),r[0]=1<t.length?t.slice(0,-1).join("-"):t[0]),!r[0].length)return!1;(e.all_lowercase||e.yahoo_lowercase)&&(r[0]=r[0].toLowerCase())}else 0<=$r.indexOf(r[1])?((e.all_lowercase||e.yandex_lowercase)&&(r[0]=r[0].toLowerCase()),r[1]="yandex.ru"):e.all_lowercase&&(r[0]=r[0].toLowerCase());return r.join("@")},toString:toString,isSlug:function(t){return u(t),hr.test(t)},isStrongPassword:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;u(t);var r,n,i=Ir(t);return(e=D(e||{},Zr)).returnScore?(r=e,n=0,n+=(t=i).uniqueChars*r.pointsPerUnique,n+=(t.length-t.uniqueChars)*r.pointsPerRepeat,0<t.lowercaseCount&&(n+=r.pointsForContainingLower),0<t.uppercaseCount&&(n+=r.pointsForContainingUpper),0<t.numberCount&&(n+=r.pointsForContainingNumber),0<t.symbolCount&&(n+=r.pointsForContainingSymbol),n):i.length>=e.minLength&&i.lowercaseCount>=e.minLowercase&&i.uppercaseCount>=e.minUppercase&&i.numberCount>=e.minNumbers&&i.symbolCount>=e.minSymbols},isTaxID:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US";if(u(t),t=t.slice(0),e in Me)return e in Ce&&(t=t.replace(Ce[e],"")),!!Me[e].test(t)&&(!(e in Be)||Be[e](t));throw new Error("Invalid locale '".concat(e,"'"))},isDate:st,isTime:function(t,e){return e=D(e,ut),"string"==typeof t&&lt[e.hourFormat][e.mode].test(t)},isLicensePlate:function(t,e){if(u(t),e in gr)return gr[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in gr)if((0,gr[r])(t))return!0;return!1},isVAT:function(t,e){if(u(t),u(e),e in Rr)return Rr[e](t);throw new Error("Invalid country code: '".concat(e,"'"))},ibanLocales:U}});