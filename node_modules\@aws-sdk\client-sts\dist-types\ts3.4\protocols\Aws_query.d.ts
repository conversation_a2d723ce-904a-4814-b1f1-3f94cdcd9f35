import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@aws-sdk/protocol-http";
import { SerdeContext as __SerdeContext } from "@aws-sdk/types";
import {
  AssumeRoleCommandInput,
  AssumeRoleCommandOutput,
} from "../commands/AssumeRoleCommand";
import {
  AssumeRoleWithSAMLCommandInput,
  AssumeRoleWithSAMLCommandOutput,
} from "../commands/AssumeRoleWithSAMLCommand";
import {
  AssumeRoleWithWebIdentityCommandInput,
  AssumeRoleWithWebIdentityCommandOutput,
} from "../commands/AssumeRoleWithWebIdentityCommand";
import {
  DecodeAuthorizationMessageCommandInput,
  DecodeAuthorizationMessageCommandOutput,
} from "../commands/DecodeAuthorizationMessageCommand";
import {
  GetAccessKeyInfoCommandInput,
  GetAccessKeyInfoCommandOutput,
} from "../commands/GetAccessKeyInfoCommand";
import {
  GetCallerIdentityCommandInput,
  GetCallerIdentityCommandOutput,
} from "../commands/GetCallerIdentityCommand";
import {
  GetFederationTokenCommandInput,
  GetFederationTokenCommandOutput,
} from "../commands/GetFederationTokenCommand";
import {
  GetSessionTokenCommandInput,
  GetSessionTokenCommandOutput,
} from "../commands/GetSessionTokenCommand";
export declare const se_AssumeRoleCommand: (
  input: AssumeRoleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_AssumeRoleWithSAMLCommand: (
  input: AssumeRoleWithSAMLCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_AssumeRoleWithWebIdentityCommand: (
  input: AssumeRoleWithWebIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DecodeAuthorizationMessageCommand: (
  input: DecodeAuthorizationMessageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetAccessKeyInfoCommand: (
  input: GetAccessKeyInfoCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetCallerIdentityCommand: (
  input: GetCallerIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetFederationTokenCommand: (
  input: GetFederationTokenCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetSessionTokenCommand: (
  input: GetSessionTokenCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_AssumeRoleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AssumeRoleCommandOutput>;
export declare const de_AssumeRoleWithSAMLCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AssumeRoleWithSAMLCommandOutput>;
export declare const de_AssumeRoleWithWebIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AssumeRoleWithWebIdentityCommandOutput>;
export declare const de_DecodeAuthorizationMessageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DecodeAuthorizationMessageCommandOutput>;
export declare const de_GetAccessKeyInfoCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetAccessKeyInfoCommandOutput>;
export declare const de_GetCallerIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetCallerIdentityCommandOutput>;
export declare const de_GetFederationTokenCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetFederationTokenCommandOutput>;
export declare const de_GetSessionTokenCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetSessionTokenCommandOutput>;
