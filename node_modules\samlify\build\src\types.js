"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceProviderMetadata = exports.ServiceProviderConstructor = exports.IdentityProviderMetadata = exports.IdentityProviderConstructor = void 0;
var entity_idp_1 = require("./entity-idp");
Object.defineProperty(exports, "IdentityProviderConstructor", { enumerable: true, get: function () { return entity_idp_1.IdentityProvider; } });
var metadata_idp_1 = require("./metadata-idp");
Object.defineProperty(exports, "IdentityProviderMetadata", { enumerable: true, get: function () { return metadata_idp_1.IdpMetadata; } });
var entity_sp_1 = require("./entity-sp");
Object.defineProperty(exports, "ServiceProviderConstructor", { enumerable: true, get: function () { return entity_sp_1.ServiceProvider; } });
var metadata_sp_1 = require("./metadata-sp");
Object.defineProperty(exports, "ServiceProviderMetadata", { enumerable: true, get: function () { return metadata_sp_1.SpMetadata; } });
//# sourceMappingURL=types.js.map