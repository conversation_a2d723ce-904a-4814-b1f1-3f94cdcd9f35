import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import {
  Handler,
  HttpHandlerOptions as __HttpHandlerOptions,
  MetadataBearer as __MetadataBearer,
  MiddlewareStack,
} from "@aws-sdk/types";
import {
  AssumeRoleWithWebIdentityRequest,
  AssumeRoleWithWebIdentityResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  STSClientResolvedConfig,
} from "../STSClient";
export interface AssumeRoleWithWebIdentityCommandInput
  extends AssumeRoleWithWebIdentityRequest {}
export interface AssumeRoleWithWebIdentityCommandOutput
  extends AssumeRoleWithWebIdentityResponse,
    __MetadataBearer {}
export declare class AssumeRoleWithWebIdentityCommand extends $Command<
  AssumeRoleWithWebIdentityCommandInput,
  AssumeRoleWithWebIdentityCommandOutput,
  STSClientResolvedConfig
> {
  readonly input: AssumeRoleWithWebIdentityCommandInput;
  static getEndpointParameterInstructions(): EndpointParameterInstructions;
  constructor(input: AssumeRoleWithWebIdentityCommandInput);
  resolveMiddleware(
    clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>,
    configuration: STSClientResolvedConfig,
    options?: __HttpHandlerOptions
  ): Handler<
    AssumeRoleWithWebIdentityCommandInput,
    AssumeRoleWithWebIdentityCommandOutput
  >;
  private serialize;
  private deserialize;
}
