{"version": 3, "file": "binding-simplesign.js", "sourceRoot": "", "sources": ["../../src/binding-simplesign.ts"], "names": [], "mappings": ";AAAA;;;;EAIE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEF,6BAA4C;AAE5C,sDAAgC;AAChC,mDAAyC;AAEzC,IAAM,OAAO,GAAG,aAAO,CAAC,OAAO,CAAC;AAChC,IAAM,SAAS,GAAG,aAAO,CAAC,SAAS,CAAC;AAgBpC;;;;;;;EAOE;AACF,SAAS,MAAM,CAAC,KAAa,EAAE,KAAa,EAAE,KAAe;IAC3D,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC;AAC5D,CAAC;AACD;;;;;;;EAOE;AACF,SAAS,oBAAoB,CAAC,IAA2B;IAErD,IAAA,IAAI,GAGF,IAAI,KAHF,EACJ,OAAO,GAEL,IAAI,QAFC,EACP,aAAa,GACX,IAAI,cADO,CACN;IACH,IAAA,KAAoB,IAAI,WAAT,EAAf,UAAU,mBAAG,EAAE,KAAA,CAAU;IAC/B,IAAM,UAAU,GAAG,iBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAErD,IAAI,UAAU,KAAK,EAAE,EAAE;QACrB,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;KACvD;IAED,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,CAAC,yBAAyB,CAAC,CAAC;IACjF,IAAM,WAAW,GAAG,OAAO,GAAG,UAAU,GAAG,MAAM,CAAC;IAClD,OAAO,iBAAO,CAAC,yBAAyB,CACtC,UAAU,GAAG,GAAG,GAAG,WAAW,EAC9B,aAAa,CAAC,UAAU,EACxB,aAAa,CAAC,cAAc,EAC5B,SAAS,EACT,aAAa,CAAC,yBAAyB,CACxC,CAAC,QAAQ,EAAE,CAAC;AACf,CAAC;AAED;;;;;EAKE;AACF,SAAS,kBAAkB,CAAC,MAAW,EAAE,oBAA2D;IAClG,IAAM,QAAQ,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;IAC1E,IAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC;IAC1C,IAAI,EAAE,GAAW,EAAE,CAAC;IAEpB,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE;QAC3C,IAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrE,IAAI,cAAc,SAAQ,CAAC;QAC3B,IAAI,SAAS,CAAC,oBAAoB,IAAI,oBAAoB,EAAE;YAC1D,IAAM,IAAI,GAAG,oBAAoB,CAAC,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC1E,EAAE,GAAG,IAAA,aAAG,EAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3B,cAAc,GAAG,IAAA,aAAG,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAC7C;aAAM;YACL,IAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;YAC5C,IAAM,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC1F,EAAE,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YAC5B,cAAc,GAAG,iBAAO,CAAC,kBAAkB,CAAC,iBAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE;gBACvF,EAAE,EAAE,EAAE;gBACN,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;gBACjC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,2BAA2B,EAAE,QAAQ,CAAC,EAAE,CAAC,2BAA2B,CAAC,OAAO,CAAC,UAAU,CAAC;gBACxF,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,YAAY,EAAE,oBAAoB;aAC5B,CAAC,CAAC;SACX;QAED,IAAI,sBAAsB,GAAS,IAAI,CAAC;QACxC,IAAI,QAAQ,CAAC,GAAG,CAAC,yBAAyB,EAAE,EAAE;YAC1C,IAAM,eAAe,GAAG,oBAAoB,CAAC;gBACzC,IAAI,EAAE,SAAS,CAAC,WAAW;gBAC3B,OAAO,EAAE,cAAc;gBACvB,aAAa,EAAE,SAAS;gBACxB,UAAU,EAAE,SAAS,CAAC,UAAU;aACnC,CAAC,CAAC;YAEH,sBAAsB,GAAG;gBACvB,SAAS,EAAE,eAAe;gBAC1B,MAAM,EAAE,SAAS,CAAC,yBAAyB;aAC5C,CAAC;SACL;QACD,mCAAmC;QACnC,kBACE,EAAE,IAAA,EACF,OAAO,EAAE,iBAAO,CAAC,YAAY,CAAC,cAAc,CAAC,IAC1C,sBAAsB,EACzB;KACH;IACD,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;AACjF,CAAC;AACD;;;;;;;EAOE;AACF,SAAe,mBAAmB,CAAC,WAAqB,EAAE,MAAW,EAAE,IAAc,EAAE,UAAmB,EAAE,oBAA2D;IAApI,4BAAA,EAAA,gBAAqB;IAAe,qBAAA,EAAA,SAAc;;;;YAC7E,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC;YACtC,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC;YACpC,EAAE,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;YAC7B,QAAQ,GAAG;gBACf,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU;gBAC1B,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU;aACzB,CAAC;YACI,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;YACvC,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC1F,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACrC,IAAI,GAAG,QAAQ,CAAC,EAAE,CAAC,2BAA2B,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACrE,eAAe,SAAQ,CAAC;gBACtB,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAErB,oBAAoB,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,MAAO,CAAE,CAAC;gBAC9D,MAAM,GAAQ;oBAClB,EAAE,EAAE,EAAE;oBACN,WAAW,EAAE,UAAU,CAAC,UAAU,EAAE;oBACpC,WAAW,EAAE,IAAI;oBACjB,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;oBACnC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;oBACnC,gBAAgB,EAAE,IAAI;oBACtB,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;oBAClC,YAAY,EAAE,OAAO,CAAC,WAAW,EAAE;oBACnC,2BAA2B,EAAE,IAAI;oBACjC,UAAU,EAAE,gBAAU,CAAC,OAAO;oBAC9B,oBAAoB;oBACpB,mBAAmB,EAAE,OAAO,CAAC,WAAW,EAAE;oBAC1C,sBAAsB,EAAE,oBAAoB,CAAC,WAAW,EAAE;oBAC1D,mCAAmC,EAAE,oBAAoB,CAAC,WAAW,EAAE;oBACvE,YAAY,EAAE,oBAAoB;oBAClC,MAAM,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;oBACxB,YAAY,EAAE,IAAA,aAAG,EAAC,WAAW,EAAE,oBAAoB,EAAE,EAAE,CAAC;oBACxD,cAAc,EAAE,EAAE;oBAClB,kBAAkB,EAAE,EAAE;iBACvB,CAAC;gBACF,IAAI,UAAU,CAAC,qBAAqB,IAAI,oBAAoB,EAAE;oBACtD,QAAQ,GAAG,oBAAoB,CAAC,UAAU,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;oBAChF,eAAe,GAAG,IAAA,aAAG,EAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;iBAClD;qBAAM;oBACL,IAAI,WAAW,KAAK,IAAI,EAAE;wBACxB,MAAM,CAAC,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;qBACtD;oBACD,eAAe,GAAG,iBAAO,CAAC,kBAAkB,CAAC,iBAAO,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;iBACpG;gBACO,UAAU,GAAoE,UAAU,WAA9E,EAAE,cAAc,GAAoD,UAAU,eAA9D,EAA6B,kBAAkB,GAAK,UAAU,0BAAf,CAAgB;gBAC3F,MAAM,GAAG;oBACb,UAAU,YAAA;oBACV,cAAc,gBAAA;oBACd,kBAAkB,oBAAA;oBAClB,WAAW,EAAE,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC;oBACvD,cAAc,EAAE,KAAK;iBACtB,CAAC;gBACF,0DAA0D;gBAC1D,IAAI,QAAQ,CAAC,EAAE,CAAC,sBAAsB,EAAE,EAAE;oBACxC,eAAe,GAAG,iBAAO,CAAC,sBAAsB,uBAC3C,MAAM,KACT,cAAc,EAAE,eAAe,EAC/B,wBAAwB,EAAE,SAAS,CAAC,wBAAwB,EAC5D,iBAAiB,EAAE,2DAA2D,EAC9E,eAAe,EAAE;4BACf,MAAM,EAAE,IAAI;4BACZ,QAAQ,EAAE,EAAE,SAAS,EAAE,qFAAqF,EAAE,MAAM,EAAE,OAAO,EAAE;yBAChI,IACD,CAAC;iBACJ;gBAGG,eAAe,GAAW,EAAE,CAAC;gBACjC,uEAAuE;gBACvE,eAAe,GAAG,oBAAoB,CAAC;oBACnC,IAAI,EAAE,SAAS,CAAC,YAAY;oBAC5B,OAAO,EAAE,eAAe;oBACxB,aAAa,EAAE,UAAU;oBACzB,UAAU,EAAE,UAAU;iBACzB,CAAE,CAAC;gBAEJ,sBAAO,OAAO,CAAC,OAAO,CAAC;wBACrB,EAAE,IAAA;wBACF,OAAO,EAAE,iBAAO,CAAC,YAAY,CAAC,eAAe,CAAC;wBAC9C,SAAS,EAAE,eAAe;wBAC1B,MAAM,EAAE,UAAU,CAAC,yBAAyB;qBAC7C,CAAC,EAAC;aAEJ;YACD,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;;;CACjF;AAED,IAAM,iBAAiB,GAAG;IACtB,kBAAkB,oBAAA;IAClB,mBAAmB,qBAAA;CACpB,CAAC;AAEJ,kBAAe,iBAAiB,CAAC"}