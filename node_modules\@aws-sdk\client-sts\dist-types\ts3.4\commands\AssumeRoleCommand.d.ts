import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import {
  Handler,
  HttpHandlerOptions as __HttpHandlerOptions,
  MetadataBearer as __MetadataBearer,
  MiddlewareStack,
} from "@aws-sdk/types";
import { AssumeRoleRequest, AssumeRoleResponse } from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  STSClientResolvedConfig,
} from "../STSClient";
export interface AssumeRoleCommandInput extends AssumeRoleRequest {}
export interface AssumeRoleCommandOutput
  extends AssumeRoleResponse,
    __MetadataBearer {}
export declare class AssumeRoleCommand extends $Command<
  AssumeRoleCommandInput,
  AssumeRoleCommandOutput,
  STSClientResolvedConfig
> {
  readonly input: AssumeRoleCommandInput;
  static getEndpointParameterInstructions(): EndpointParameterInstructions;
  constructor(input: AssumeRoleCommandInput);
  resolveMiddleware(
    clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>,
    configuration: STSClientResolvedConfig,
    options?: __HttpHandlerOptions
  ): Handler<AssumeRoleCommandInput, AssumeRoleCommandOutput>;
  private serialize;
  private deserialize;
}
