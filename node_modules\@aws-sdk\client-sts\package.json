{"name": "@aws-sdk/client-sts", "description": "AWS SDK for JavaScript Sts Client for Node.js, Browser and React Native", "version": "3.332.0", "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'", "build:cjs": "tsc -p tsconfig.cjs.json", "build:docs": "typedoc", "build:es": "tsc -p tsconfig.es.json", "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build", "build:types": "tsc -p tsconfig.types.json", "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo", "extract:docs": "api-extractor run --local", "generate:client": "node ../../scripts/generate-clients/single-service --solo sts", "test": "yarn test:unit", "test:unit": "jest"}, "main": "./dist-cjs/index.js", "types": "./dist-types/index.d.ts", "module": "./dist-es/index.js", "sideEffects": false, "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/config-resolver": "3.329.0", "@aws-sdk/credential-provider-node": "3.332.0", "@aws-sdk/fetch-http-handler": "3.329.0", "@aws-sdk/hash-node": "3.329.0", "@aws-sdk/invalid-dependency": "3.329.0", "@aws-sdk/middleware-content-length": "3.329.0", "@aws-sdk/middleware-endpoint": "3.329.0", "@aws-sdk/middleware-host-header": "3.329.0", "@aws-sdk/middleware-logger": "3.329.0", "@aws-sdk/middleware-recursion-detection": "3.329.0", "@aws-sdk/middleware-retry": "3.329.0", "@aws-sdk/middleware-sdk-sts": "3.329.0", "@aws-sdk/middleware-serde": "3.329.0", "@aws-sdk/middleware-signing": "3.329.0", "@aws-sdk/middleware-stack": "3.329.0", "@aws-sdk/middleware-user-agent": "3.332.0", "@aws-sdk/node-config-provider": "3.329.0", "@aws-sdk/node-http-handler": "3.329.0", "@aws-sdk/protocol-http": "3.329.0", "@aws-sdk/smithy-client": "3.329.0", "@aws-sdk/types": "3.329.0", "@aws-sdk/url-parser": "3.329.0", "@aws-sdk/util-base64": "3.310.0", "@aws-sdk/util-body-length-browser": "3.310.0", "@aws-sdk/util-body-length-node": "3.310.0", "@aws-sdk/util-defaults-mode-browser": "3.329.0", "@aws-sdk/util-defaults-mode-node": "3.329.0", "@aws-sdk/util-endpoints": "3.332.0", "@aws-sdk/util-retry": "3.329.0", "@aws-sdk/util-user-agent-browser": "3.329.0", "@aws-sdk/util-user-agent-node": "3.329.0", "@aws-sdk/util-utf8": "3.310.0", "fast-xml-parser": "4.1.2", "tslib": "^2.5.0"}, "devDependencies": {"@aws-sdk/service-client-documentation-generator": "3.310.0", "@tsconfig/node14": "1.0.3", "@types/node": "^14.14.31", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typedoc": "0.23.23", "typescript": "~4.9.5"}, "engines": {"node": ">=14.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "browser": {"./dist-es/runtimeConfig": "./dist-es/runtimeConfig.browser"}, "react-native": {"./dist-es/runtimeConfig": "./dist-es/runtimeConfig.native"}, "homepage": "https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sts", "repository": {"type": "git", "url": "https://github.com/aws/aws-sdk-js-v3.git", "directory": "clients/client-sts"}}