{"version": 3, "file": "utility.js", "sourceRoot": "", "sources": ["../../src/utility.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;EAIE;AACF,yCAA6C;AAC7C,6BAAwC;AAExC,IAAM,UAAU,GAAG,QAAQ,CAAC;AAE5B;;;;GAIG;AACH,SAAgB,SAAS,CAAC,IAAc,EAAE,IAAW,EAAE,cAAqB;IAArB,+BAAA,EAAA,qBAAqB;IAC1E,OAAO,IAAI,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC,EAAE,CAAC;QAE3B,IAAI,cAAc,EAAE;YAClB,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACjB,OAAO,GAAG,CAAC;SACZ;QACD,2EAA2E;QAC3E,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACxB,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,OAAO,GAAG,CAAC;SACZ;QAED,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACjB,OAAO,GAAG,CAAC;IAEb,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAnBD,8BAmBC;AACD;;;;GAIG;AACH,SAAgB,WAAW,CAAC,KAAY;IACtC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC,KAAK,CAAC,MAAM,CAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAxB,CAAwB,EAAG,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACZ,CAAC;AAJD,kCAIC;AACD;;;;GAIG;AACH,SAAgB,IAAI,CAAC,KAAY;IAC/B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AAFD,oBAEC;AACD;;;;GAIG;AACH,SAAgB,IAAI,CAAC,KAAe;IAClC,IAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IAC3B,gCAAY,GAAG,UAAE;AACnB,CAAC;AAHD,oBAGC;AACD;;;;;;GAMG;AACH,SAAgB,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,YAAY;IACzC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;SACrB,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,EAA3C,CAA2C,EAAE,GAAG,CAAC,CAAC;AACtE,CAAC;AAHD,kBAGC;AACD;;;GAGG;AACH,SAAgB,QAAQ,CAAC,KAAU;IACjC,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;AACnC,CAAC;AAFD,4BAEC;AACD;;;;EAIE;AACF,SAAS,YAAY,CAAC,OAA0B;IAC9C,OAAO,MAAM,CAAC,IAAI,CAAC,OAAiB,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC7D,CAAC;AACD;;;;;EAKE;AACF,SAAgB,YAAY,CAAC,aAAqB,EAAE,OAAiB;IACnE,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IACrD,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;AACrD,CAAC;AAHD,oCAGC;AACD;;;;EAIE;AACF,SAAS,aAAa,CAAC,OAAe;IACpC,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAC;IAC5E,OAAO,KAAK,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,KAAK,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACnD,CAAC;AACD;;;;EAIE;AACF,SAAgB,aAAa,CAAC,gBAAwB;IACpD,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;IAC9D,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC,CAAC;IACnG,OAAO,KAAK,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,KAAK,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;SAC7C,GAAG,CAAC,UAAC,IAAY,IAAK,OAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAzB,CAAyB,CAAC;SAChD,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAND,sCAMC;AACD;;;;;EAKE;AACF,SAAS,mBAAmB,CAAC,GAAoB,EAAE,MAAc;IAC/D,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,qBAAc,MAAM,UAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,mBAAY,MAAM,UAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnL,CAAC;AACD;;;;EAIE;AACF,SAAS,kBAAkB,CAAC,UAA2B;IACrD,OAAO,mBAAmB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AACxD,CAAC;AACD;;;;EAIE;AACF,SAAS,kBAAkB,CAAC,SAA0B;IACpD,OAAO,mBAAmB,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;AACtE,CAAC;AACD;;;;EAIE;AACF,SAAS,UAAU,CAAC,GAAG;IACrB,OAAO,UAAG,GAAG,CAAC,QAAQ,gBAAM,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,SAAG,GAAG,CAAC,WAAW,CAAE,CAAC;AAClE,CAAC;AACD;;;;EAIE;AACF,SAAS,WAAW,CAAC,GAAG,EAAE,YAAiB;IAAjB,6BAAA,EAAA,iBAAiB;IACzC,OAAO,GAAG,IAAI,YAAY,CAAC;AAC7B,CAAC;AACD;;;;;EAKE;AACF,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI;IAC9B,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC;AACD;;;;EAIE;AACF,SAAS,8BAA8B,CAAC,eAAuB;IAC7D,IAAM,YAAY,GAAG,iBAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;IACpD,IAAM,GAAG,GAAG,iBAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACvC,IAAM,IAAI,GAAG,gBAAG,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAC1C,OAAO,gBAAG,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AACD;;;;;;EAME;AACF,SAAgB,cAAc,CAAC,SAA0B,EAAE,UAA8B,EAAE,cAAwB;IACjH,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAG,CAAC,eAAe,CAAC,gBAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC/J,CAAC;AAFD,wCAEC;AACD;;EAEE;AACF,SAAS,eAAe,CAAC,KAAK,EAAE,cAAc;IAC5C,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACzD,CAAC;AACD;;GAEG;AACH,SAAgB,eAAe,CAAC,CAAC;IAC/B,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1C,CAAC;AAFD,0CAEC;AAED,SAAgB,YAAY,CAAI,CAAW;IACzC,IAAI,CAAC,KAAK,SAAS;QAAE,OAAO,EAAE,CAAA;IAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACnC,CAAC;AAHD,oCAGC;AAED,SAAgB,QAAQ,CAAS,KAAgC;IAC/D,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC;AAFD,4BAEC;AAED,IAAM,OAAO,GAAG;IACd,QAAQ,UAAA;IACR,YAAY,cAAA;IACZ,YAAY,cAAA;IACZ,aAAa,eAAA;IACb,aAAa,eAAA;IACb,kBAAkB,oBAAA;IAClB,kBAAkB,oBAAA;IAClB,UAAU,YAAA;IACV,WAAW,aAAA;IACX,YAAY,cAAA;IACZ,8BAA8B,gCAAA;IAC9B,cAAc,gBAAA;IACd,eAAe,iBAAA;IACf,eAAe,iBAAA;CAChB,CAAC;AAEF,kBAAe,OAAO,CAAC"}