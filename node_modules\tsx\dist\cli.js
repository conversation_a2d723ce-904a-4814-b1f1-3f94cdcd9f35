#!/usr/bin/env node
import ys from"tty";import{v as ws}from"./package-2c948d8d.js";import{r as Dt}from"./pkgroll_create-require-040ba28b.js";import{pathToFileURL as Rs,fileURLToPath as bs}from"url";import vs from"child_process";import K from"path";import ie from"fs";import Fu,{constants as Bs}from"os";import Ss from"events";import me from"util";import $s from"stream";import"module";const Ts="known-flag",xs="unknown-flag",Os="argument",{stringify:_e}=JSON,Ns=/\B([A-Z])/g,Hs=t=>t.replace(Ns,"-$1").toLowerCase(),{hasOwnProperty:Ps}=Object.prototype,Ae=(t,e)=>Ps.call(t,e),Ls=t=>Array.isArray(t),gu=t=>typeof t=="function"?[t,!1]:Ls(t)?[t[0],!0]:gu(t.type),Is=(t,e)=>t===Boolean?e!=="false":e,ks=(t,e)=>typeof e=="boolean"?e:t===Number&&e===""?Number.NaN:t(e),Ms=/[\s.:=]/,Ws=t=>{const e=`Flag name ${_e(t)}`;if(t.length===0)throw new Error(`${e} cannot be empty`);if(t.length===1)throw new Error(`${e} must be longer than a character`);const u=t.match(Ms);if(u)throw new Error(`${e} cannot contain ${_e(u==null?void 0:u[0])}`)},Gs=t=>{const e={},u=(n,s)=>{if(Ae(e,n))throw new Error(`Duplicate flags named ${_e(n)}`);e[n]=s};for(const n in t){if(!Ae(t,n))continue;Ws(n);const s=t[n],r=[[],...gu(s),s];u(n,r);const i=Hs(n);if(n!==i&&u(i,r),"alias"in s&&typeof s.alias=="string"){const{alias:D}=s,o=`Flag alias ${_e(D)} for flag ${_e(n)}`;if(D.length===0)throw new Error(`${o} cannot be empty`);if(D.length>1)throw new Error(`${o} must be a single character`);u(D,r)}}return e},js=(t,e)=>{const u={};for(const n in t){if(!Ae(t,n))continue;const[s,,r,i]=e[n];if(s.length===0&&"default"in i){let{default:D}=i;typeof D=="function"&&(D=D()),u[n]=D}else u[n]=r?s:s.pop()}return u},Le="--",Us=/[.:=]/,Ks=/^-{1,2}\w/,Vs=t=>{if(!Ks.test(t))return;const e=!t.startsWith(Le);let u=t.slice(e?1:2),n;const s=u.match(Us);if(s){const{index:r}=s;n=u.slice(r+1),u=u.slice(0,r)}return[u,n,e]},zs=(t,{onFlag:e,onArgument:u})=>{let n;const s=(r,i)=>{if(typeof n!="function")return!0;n(r,i),n=void 0};for(let r=0;r<t.length;r+=1){const i=t[r];if(i===Le){s();const o=t.slice(r+1);u==null||u(o,[r],!0);break}const D=Vs(i);if(D){if(s(),!e)continue;const[o,a,c]=D;if(c)for(let f=0;f<o.length;f+=1){s();const l=f===o.length-1;n=e(o[f],l?a:void 0,[r,f+1,l])}else n=e(o,a,[r])}else s(i,[r])&&(u==null||u([i],[r]))}s()},Ys=(t,e)=>{for(const[u,n,s]of e.reverse()){if(n){const r=t[u];let i=r.slice(0,n);if(s||(i+=r.slice(n+1)),i!=="-"){t[u]=i;continue}}t.splice(u,1)}},mu=(t,e=process.argv.slice(2),{ignore:u}={})=>{const n=[],s=Gs(t),r={},i=[];return i[Le]=[],zs(e,{onFlag(D,o,a){const c=Ae(s,D);if(!(u!=null&&u(c?Ts:xs,D,o))){if(c){const[f,l]=s[D],p=Is(l,o),C=(F,A)=>{n.push(a),A&&n.push(A),f.push(ks(l,F||""))};return p===void 0?C:C(p)}Ae(r,D)||(r[D]=[]),r[D].push(o===void 0?!0:o),n.push(a)}},onArgument(D,o,a){u!=null&&u(Os,e[o[0]])||(i.push(...D),a?(i[Le]=D,e.splice(o[0])):n.push(o))}}),Ys(e,n),{flags:js(t,s),unknownFlags:r,_:i}};var qs=Object.create,Ie=Object.defineProperty,Xs=Object.defineProperties,Qs=Object.getOwnPropertyDescriptor,Zs=Object.getOwnPropertyDescriptors,Js=Object.getOwnPropertyNames,_u=Object.getOwnPropertySymbols,er=Object.getPrototypeOf,Au=Object.prototype.hasOwnProperty,tr=Object.prototype.propertyIsEnumerable,yu=(t,e,u)=>e in t?Ie(t,e,{enumerable:!0,configurable:!0,writable:!0,value:u}):t[e]=u,ke=(t,e)=>{for(var u in e||(e={}))Au.call(e,u)&&yu(t,u,e[u]);if(_u)for(var u of _u(e))tr.call(e,u)&&yu(t,u,e[u]);return t},ot=(t,e)=>Xs(t,Zs(e)),ur=t=>Ie(t,"__esModule",{value:!0}),nr=(t,e)=>()=>(t&&(e=t(t=0)),e),sr=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),rr=(t,e,u,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of Js(e))!Au.call(t,s)&&(u||s!=="default")&&Ie(t,s,{get:()=>e[s],enumerable:!(n=Qs(e,s))||n.enumerable});return t},ir=(t,e)=>rr(ur(Ie(t!=null?qs(er(t)):{},"default",!e&&t&&t.__esModule?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t),U=nr(()=>{}),Dr=sr((t,e)=>{U(),e.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});U(),U(),U();var or=t=>{var e,u,n;let s=(e=process.stdout.columns)!=null?e:Number.POSITIVE_INFINITY;return typeof t=="function"&&(t=t(s)),t||(t={}),Array.isArray(t)?{columns:t,stdoutColumns:s}:{columns:(u=t.columns)!=null?u:[],stdoutColumns:(n=t.stdoutColumns)!=null?n:s}};U(),U(),U(),U(),U();function ar({onlyFirst:t=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}function wu(t){if(typeof t!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof t}\``);return t.replace(ar(),"")}U();function lr(t){return Number.isInteger(t)?t>=4352&&(t<=4447||t===9001||t===9002||11904<=t&&t<=12871&&t!==12351||12880<=t&&t<=19903||19968<=t&&t<=42182||43360<=t&&t<=43388||44032<=t&&t<=55203||63744<=t&&t<=64255||65040<=t&&t<=65049||65072<=t&&t<=65131||65281<=t&&t<=65376||65504<=t&&t<=65510||110592<=t&&t<=110593||127488<=t&&t<=127569||131072<=t&&t<=262141):!1}var cr=ir(Dr(),1);function De(t){if(typeof t!="string"||t.length===0||(t=wu(t),t.length===0))return 0;t=t.replace((0,cr.default)(),"  ");let e=0;for(let u=0;u<t.length;u++){let n=t.codePointAt(u);n<=31||n>=127&&n<=159||n>=768&&n<=879||(n>65535&&u++,e+=lr(n)?2:1)}return e}var Ru=t=>Math.max(...t.split(`
`).map(De)),fr=t=>{let e=[];for(let u of t){let{length:n}=u,s=n-e.length;for(let r=0;r<s;r+=1)e.push(0);for(let r=0;r<n;r+=1){let i=Ru(u[r]);i>e[r]&&(e[r]=i)}}return e};U();var bu=/^\d+%$/,vu={width:"auto",align:"left",contentWidth:0,paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0,horizontalPadding:0,paddingLeftString:"",paddingRightString:""},hr=(t,e)=>{var u;let n=[];for(let s=0;s<t.length;s+=1){let r=(u=e[s])!=null?u:"auto";if(typeof r=="number"||r==="auto"||r==="content-width"||typeof r=="string"&&bu.test(r)){n.push(ot(ke({},vu),{width:r,contentWidth:t[s]}));continue}if(r&&typeof r=="object"){let i=ot(ke(ke({},vu),r),{contentWidth:t[s]});i.horizontalPadding=i.paddingLeft+i.paddingRight,n.push(i);continue}throw new Error(`Invalid column width: ${JSON.stringify(r)}`)}return n};function dr(t,e){for(let u of t){let{width:n}=u;if(n==="content-width"&&(u.width=u.contentWidth),n==="auto"){let o=Math.min(20,u.contentWidth);u.width=o,u.autoOverflow=u.contentWidth-o}if(typeof n=="string"&&bu.test(n)){let o=Number.parseFloat(n.slice(0,-1))/100;u.width=Math.floor(e*o)-(u.paddingLeft+u.paddingRight)}let{horizontalPadding:s}=u,r=1,i=r+s;if(i>=e){let o=i-e,a=Math.ceil(u.paddingLeft/s*o),c=o-a;u.paddingLeft-=a,u.paddingRight-=c,u.horizontalPadding=u.paddingLeft+u.paddingRight}u.paddingLeftString=u.paddingLeft?" ".repeat(u.paddingLeft):"",u.paddingRightString=u.paddingRight?" ".repeat(u.paddingRight):"";let D=e-u.horizontalPadding;u.width=Math.max(Math.min(u.width,D),r)}}var Bu=()=>Object.assign([],{columns:0});function Er(t,e){let u=[Bu()],[n]=u;for(let s of t){let r=s.width+s.horizontalPadding;n.columns+r>e&&(n=Bu(),u.push(n)),n.push(s),n.columns+=r}for(let s of u){let r=s.reduce((l,p)=>l+p.width+p.horizontalPadding,0),i=e-r;if(i===0)continue;let D=s.filter(l=>"autoOverflow"in l),o=D.filter(l=>l.autoOverflow>0),a=o.reduce((l,p)=>l+p.autoOverflow,0),c=Math.min(a,i);for(let l of o){let p=Math.floor(l.autoOverflow/a*c);l.width+=p,i-=p}let f=Math.floor(i/D.length);for(let l=0;l<D.length;l+=1){let p=D[l];l===D.length-1?p.width+=i:p.width+=f,i-=f}}return u}function pr(t,e,u){let n=hr(u,e);return dr(n,t),Er(n,t)}U(),U(),U();var at=10,Su=(t=0)=>e=>`\x1B[${e+t}m`,$u=(t=0)=>e=>`\x1B[${38+t};5;${e}m`,Tu=(t=0)=>(e,u,n)=>`\x1B[${38+t};2;${e};${u};${n}m`;function Cr(){let t=new Map,e={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};e.color.gray=e.color.blackBright,e.bgColor.bgGray=e.bgColor.bgBlackBright,e.color.grey=e.color.blackBright,e.bgColor.bgGrey=e.bgColor.bgBlackBright;for(let[u,n]of Object.entries(e)){for(let[s,r]of Object.entries(n))e[s]={open:`\x1B[${r[0]}m`,close:`\x1B[${r[1]}m`},n[s]=e[s],t.set(r[0],r[1]);Object.defineProperty(e,u,{value:n,enumerable:!1})}return Object.defineProperty(e,"codes",{value:t,enumerable:!1}),e.color.close="\x1B[39m",e.bgColor.close="\x1B[49m",e.color.ansi=Su(),e.color.ansi256=$u(),e.color.ansi16m=Tu(),e.bgColor.ansi=Su(at),e.bgColor.ansi256=$u(at),e.bgColor.ansi16m=Tu(at),Object.defineProperties(e,{rgbToAnsi256:{value:(u,n,s)=>u===n&&n===s?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(n/255*5)+Math.round(s/255*5),enumerable:!1},hexToRgb:{value:u=>{let n=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(u.toString(16));if(!n)return[0,0,0];let{colorString:s}=n.groups;s.length===3&&(s=s.split("").map(i=>i+i).join(""));let r=Number.parseInt(s,16);return[r>>16&255,r>>8&255,r&255]},enumerable:!1},hexToAnsi256:{value:u=>e.rgbToAnsi256(...e.hexToRgb(u)),enumerable:!1},ansi256ToAnsi:{value:u=>{if(u<8)return 30+u;if(u<16)return 90+(u-8);let n,s,r;if(u>=232)n=((u-232)*10+8)/255,s=n,r=n;else{u-=16;let o=u%36;n=Math.floor(u/36)/5,s=Math.floor(o/6)/5,r=o%6/5}let i=Math.max(n,s,r)*2;if(i===0)return 30;let D=30+(Math.round(r)<<2|Math.round(s)<<1|Math.round(n));return i===2&&(D+=60),D},enumerable:!1},rgbToAnsi:{value:(u,n,s)=>e.ansi256ToAnsi(e.rgbToAnsi256(u,n,s)),enumerable:!1},hexToAnsi:{value:u=>e.ansi256ToAnsi(e.hexToAnsi256(u)),enumerable:!1}}),e}var Fr=Cr(),gr=Fr,Me=new Set(["\x1B","\x9B"]),mr=39,lt="\x07",xu="[",_r="]",Ou="m",ct=`${_r}8;;`,Nu=t=>`${Me.values().next().value}${xu}${t}${Ou}`,Hu=t=>`${Me.values().next().value}${ct}${t}${lt}`,Ar=t=>t.split(" ").map(e=>De(e)),ft=(t,e,u)=>{let n=[...e],s=!1,r=!1,i=De(wu(t[t.length-1]));for(let[D,o]of n.entries()){let a=De(o);if(i+a<=u?t[t.length-1]+=o:(t.push(o),i=0),Me.has(o)&&(s=!0,r=n.slice(D+1).join("").startsWith(ct)),s){r?o===lt&&(s=!1,r=!1):o===Ou&&(s=!1);continue}i+=a,i===u&&D<n.length-1&&(t.push(""),i=0)}!i&&t[t.length-1].length>0&&t.length>1&&(t[t.length-2]+=t.pop())},yr=t=>{let e=t.split(" "),u=e.length;for(;u>0&&!(De(e[u-1])>0);)u--;return u===e.length?t:e.slice(0,u).join(" ")+e.slice(u).join("")},wr=(t,e,u={})=>{if(u.trim!==!1&&t.trim()==="")return"";let n="",s,r,i=Ar(t),D=[""];for(let[a,c]of t.split(" ").entries()){u.trim!==!1&&(D[D.length-1]=D[D.length-1].trimStart());let f=De(D[D.length-1]);if(a!==0&&(f>=e&&(u.wordWrap===!1||u.trim===!1)&&(D.push(""),f=0),(f>0||u.trim===!1)&&(D[D.length-1]+=" ",f++)),u.hard&&i[a]>e){let l=e-f,p=1+Math.floor((i[a]-l-1)/e);Math.floor((i[a]-1)/e)<p&&D.push(""),ft(D,c,e);continue}if(f+i[a]>e&&f>0&&i[a]>0){if(u.wordWrap===!1&&f<e){ft(D,c,e);continue}D.push("")}if(f+i[a]>e&&u.wordWrap===!1){ft(D,c,e);continue}D[D.length-1]+=c}u.trim!==!1&&(D=D.map(a=>yr(a)));let o=[...D.join(`
`)];for(let[a,c]of o.entries()){if(n+=c,Me.has(c)){let{groups:l}=new RegExp(`(?:\\${xu}(?<code>\\d+)m|\\${ct}(?<uri>.*)${lt})`).exec(o.slice(a).join(""))||{groups:{}};if(l.code!==void 0){let p=Number.parseFloat(l.code);s=p===mr?void 0:p}else l.uri!==void 0&&(r=l.uri.length===0?void 0:l.uri)}let f=gr.codes.get(Number(s));o[a+1]===`
`?(r&&(n+=Hu("")),s&&f&&(n+=Nu(f))):c===`
`&&(s&&f&&(n+=Nu(s)),r&&(n+=Hu(r)))}return n};function Rr(t,e,u){return String(t).normalize().replace(/\r\n/g,`
`).split(`
`).map(n=>wr(n,e,u)).join(`
`)}var Pu=t=>Array.from({length:t}).fill("");function br(t,e){let u=[],n=0;for(let s of t){let r=0,i=s.map(o=>{var a;let c=(a=e[n])!=null?a:"";n+=1,o.preprocess&&(c=o.preprocess(c)),Ru(c)>o.width&&(c=Rr(c,o.width,{hard:!0}));let f=c.split(`
`);if(o.postprocess){let{postprocess:l}=o;f=f.map((p,C)=>l.call(o,p,C))}return o.paddingTop&&f.unshift(...Pu(o.paddingTop)),o.paddingBottom&&f.push(...Pu(o.paddingBottom)),f.length>r&&(r=f.length),ot(ke({},o),{lines:f})}),D=[];for(let o=0;o<r;o+=1){let a=i.map(c=>{var f;let l=(f=c.lines[o])!=null?f:"",p=Number.isFinite(c.width)?" ".repeat(c.width-De(l)):"",C=c.paddingLeftString;return c.align==="right"&&(C+=p),C+=l,c.align==="left"&&(C+=p),C+c.paddingRightString}).join("");D.push(a)}u.push(D.join(`
`))}return u.join(`
`)}function vr(t,e){if(!t||t.length===0)return"";let u=fr(t),n=u.length;if(n===0)return"";let{stdoutColumns:s,columns:r}=or(e);if(r.length>n)throw new Error(`${r.length} columns defined, but only ${n} columns found`);let i=pr(s,r,u);return t.map(D=>br(i,D)).join(`
`)}U();var Br=["<",">","=",">=","<="];function Sr(t){if(!Br.includes(t))throw new TypeError(`Invalid breakpoint operator: ${t}`)}function $r(t){let e=Object.keys(t).map(u=>{let[n,s]=u.split(" ");Sr(n);let r=Number.parseInt(s,10);if(Number.isNaN(r))throw new TypeError(`Invalid breakpoint value: ${s}`);let i=t[u];return{operator:n,breakpoint:r,value:i}}).sort((u,n)=>n.breakpoint-u.breakpoint);return u=>{var n;return(n=e.find(({operator:s,breakpoint:r})=>s==="="&&u===r||s===">"&&u>r||s==="<"&&u<r||s===">="&&u>=r||s==="<="&&u<=r))==null?void 0:n.value}}const Tr=t=>t.replace(/[-_ ](\w)/g,(e,u)=>u.toUpperCase()),xr=t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase(),Or={"> 80":[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"auto"}],"> 40":[{width:"auto",paddingLeft:2,paddingRight:8,preprocess:t=>t.trim()},{width:"100%",paddingLeft:2,paddingBottom:1}],"> 0":{stdoutColumns:1e3,columns:[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"content-width"}]}};function Nr(t){let e=!1;return{type:"table",data:{tableData:Object.keys(t).sort((n,s)=>n.localeCompare(s)).map(n=>{const s=t[n],r="alias"in s;return r&&(e=!0),{name:n,flag:s,flagFormatted:`--${xr(n)}`,aliasesEnabled:e,aliasFormatted:r?`-${s.alias}`:void 0}}).map(n=>(n.aliasesEnabled=e,[{type:"flagName",data:n},{type:"flagDescription",data:n}])),tableBreakpoints:Or}}}const Lu=t=>{var e;return!t||((e=t.version)!=null?e:t.help?t.help.version:void 0)},Iu=t=>{var u;const e="parent"in t&&((u=t.parent)==null?void 0:u.name);return(e?`${e} `:"")+t.name};function Hr(t){var n;const e=[];t.name&&e.push(Iu(t));const u=(n=Lu(t))!=null?n:"parent"in t&&Lu(t.parent);if(u&&e.push(`v${u}`),e.length!==0)return{id:"name",type:"text",data:`${e.join(" ")}
`}}function Pr(t){const{help:e}=t;if(!(!e||!e.description))return{id:"description",type:"text",data:`${e.description}
`}}function Lr(t){var u;const e=t.help||{};if("usage"in e)return e.usage?{id:"usage",type:"section",data:{title:"Usage:",body:Array.isArray(e.usage)?e.usage.join(`
`):e.usage}}:void 0;if(t.name){const n=[],s=[Iu(t)];if(t.flags&&Object.keys(t.flags).length>0&&s.push("[flags...]"),t.parameters&&t.parameters.length>0){const{parameters:r}=t,i=r.indexOf("--"),D=i>-1&&r.slice(i+1).some(o=>o.startsWith("<"));s.push(r.map(o=>o!=="--"?o:D?"--":"[--]").join(" "))}if(s.length>1&&n.push(s.join(" ")),"commands"in t&&((u=t.commands)==null?void 0:u.length)&&n.push(`${t.name} <command>`),n.length>0)return{id:"usage",type:"section",data:{title:"Usage:",body:n.join(`
`)}}}}function Ir(t){var u;return!("commands"in t)||!((u=t.commands)!=null&&u.length)?void 0:{id:"commands",type:"section",data:{title:"Commands:",body:{type:"table",data:{tableData:t.commands.map(n=>[n.options.name,n.options.help?n.options.help.description:""]),tableOptions:[{width:"content-width",paddingLeft:2,paddingRight:8}]}},indentBody:0}}}function kr(t){if(!(!t.flags||Object.keys(t.flags).length===0))return{id:"flags",type:"section",data:{title:"Flags:",body:Nr(t.flags),indentBody:0}}}function Mr(t){const{help:e}=t;if(!e||!e.examples||e.examples.length===0)return;let{examples:u}=e;if(Array.isArray(u)&&(u=u.join(`
`)),u)return{id:"examples",type:"section",data:{title:"Examples:",body:u}}}function Wr(t){if(!("alias"in t)||!t.alias)return;const{alias:e}=t;return{id:"aliases",type:"section",data:{title:"Aliases:",body:Array.isArray(e)?e.join(", "):e}}}const Gr=t=>[Hr,Pr,Lr,Ir,kr,Mr,Wr].map(e=>e(t)).filter(Boolean),jr=ys.WriteStream.prototype.hasColors();class Ur{text(e){return e}bold(e){return jr?`\x1B[1m${e}\x1B[22m`:e.toLocaleUpperCase()}indentText({text:e,spaces:u}){return e.replace(/^/gm," ".repeat(u))}heading(e){return this.bold(e)}section({title:e,body:u,indentBody:n=2}){return`${(e?`${this.heading(e)}
`:"")+(u?this.indentText({text:this.render(u),spaces:n}):"")}
`}table({tableData:e,tableOptions:u,tableBreakpoints:n}){return vr(e.map(s=>s.map(r=>this.render(r))),n?$r(n):u)}flagParameter(e){return e===Boolean?"":e===String?"<string>":e===Number?"<number>":Array.isArray(e)?this.flagParameter(e[0]):"<value>"}flagOperator(e){return" "}flagName(e){const{flag:u,flagFormatted:n,aliasesEnabled:s,aliasFormatted:r}=e;let i="";if(r?i+=`${r}, `:s&&(i+="    "),i+=n,"placeholder"in u&&typeof u.placeholder=="string")i+=`${this.flagOperator(e)}${u.placeholder}`;else{const D=this.flagParameter("type"in u?u.type:u);D&&(i+=`${this.flagOperator(e)}${D}`)}return i}flagDefault(e){return JSON.stringify(e)}flagDescription({flag:e}){var n;let u="description"in e&&(n=e.description)!=null?n:"";if("default"in e){let{default:s}=e;typeof s=="function"&&(s=s()),s&&(u+=` (default: ${this.flagDefault(s)})`)}return u}render(e){if(typeof e=="string")return e;if(Array.isArray(e))return e.map(u=>this.render(u)).join(`
`);if("type"in e&&this[e.type]){const u=this[e.type];if(typeof u=="function")return u.call(this,e.data)}throw new Error(`Invalid node type: ${JSON.stringify(e)}`)}}const ht=/^[\w.-]+$/,{stringify:J}=JSON,Kr=/[|\\{}()[\]^$+*?.]/;function dt(t){const e=[];let u,n;for(const s of t){if(n)throw new Error(`Invalid parameter: Spread parameter ${J(n)} must be last`);const r=s[0],i=s[s.length-1];let D;if(r==="<"&&i===">"&&(D=!0,u))throw new Error(`Invalid parameter: Required parameter ${J(s)} cannot come after optional parameter ${J(u)}`);if(r==="["&&i==="]"&&(D=!1,u=s),D===void 0)throw new Error(`Invalid parameter: ${J(s)}. Must be wrapped in <> (required parameter) or [] (optional parameter)`);let o=s.slice(1,-1);const a=o.slice(-3)==="...";a&&(n=s,o=o.slice(0,-3));const c=o.match(Kr);if(c)throw new Error(`Invalid parameter: ${J(s)}. Invalid character found ${J(c[0])}`);e.push({name:o,required:D,spread:a})}return e}function Et(t,e,u,n){for(let s=0;s<e.length;s+=1){const{name:r,required:i,spread:D}=e[s],o=Tr(r);if(o in t)throw new Error(`Invalid parameter: ${J(r)} is used more than once.`);const a=D?u.slice(s):u[s];if(D&&(s=e.length),i&&(!a||D&&a.length===0))return console.error(`Error: Missing required parameter ${J(r)}
`),n(),process.exit(1);t[o]=a}}function Vr(t){return t===void 0||t!==!1}function ku(t,e,u,n){const s={...e.flags},r=e.version;r&&(s.version={type:Boolean,description:"Show version"});const{help:i}=e,D=Vr(i);D&&!("help"in s)&&(s.help={type:Boolean,alias:"h",description:"Show help"});const o=mu(s,n,{ignore:e.ignoreArgv}),a=()=>{console.log(e.version)};if(r&&o.flags.version===!0)return a(),process.exit(0);const c=new Ur,f=D&&(i==null?void 0:i.render)?i.render:C=>c.render(C),l=C=>{const F=Gr({...e,...C?{help:C}:{},flags:s});console.log(f(F,c))};if(D&&o.flags.help===!0)return l(),process.exit(0);if(e.parameters){let{parameters:C}=e,F=o._;const A=C.indexOf("--"),B=C.slice(A+1),H=Object.create(null);if(A>-1&&B.length>0){C=C.slice(0,A);const S=o._["--"];F=F.slice(0,-S.length||void 0),Et(H,dt(C),F,l),Et(H,dt(B),S,l)}else Et(H,dt(C),F,l);Object.assign(o._,H)}const p={...o,showVersion:a,showHelp:l};return typeof u=="function"&&u(p),{command:t,...p}}function zr(t,e){const u=new Map;for(const n of e){const s=[n.options.name],{alias:r}=n.options;r&&(Array.isArray(r)?s.push(...r):s.push(r));for(const i of s){if(u.has(i))throw new Error(`Duplicate command name found: ${J(i)}`);u.set(i,n)}}return u.get(t)}function Yr(t,e,u=process.argv.slice(2)){if(!t)throw new Error("Options is required");if("name"in t&&(!t.name||!ht.test(t.name)))throw new Error(`Invalid script name: ${J(t.name)}`);const n=u[0];if(t.commands&&ht.test(n)){const s=zr(n,t.commands);if(s)return ku(s.options.name,{...s.options,parent:t},s.callback,u.slice(1))}return ku(void 0,t,e,u)}function qr(t,e){if(!t)throw new Error("Command options are required");const{name:u}=t;if(t.name===void 0)throw new Error("Command name is required");if(!ht.test(u))throw new Error(`Invalid command name ${JSON.stringify(u)}. Command names must be one word.`);return{options:t,callback:e}}var Xr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},fe={exports:{}},pt,Mu;function Qr(){if(Mu)return pt;Mu=1,pt=n,n.sync=s;var t=ie;function e(r,i){var D=i.pathExt!==void 0?i.pathExt:process.env.PATHEXT;if(!D||(D=D.split(";"),D.indexOf("")!==-1))return!0;for(var o=0;o<D.length;o++){var a=D[o].toLowerCase();if(a&&r.substr(-a.length).toLowerCase()===a)return!0}return!1}function u(r,i,D){return!r.isSymbolicLink()&&!r.isFile()?!1:e(i,D)}function n(r,i,D){t.stat(r,function(o,a){D(o,o?!1:u(a,r,i))})}function s(r,i){return u(t.statSync(r),r,i)}return pt}var Ct,Wu;function Zr(){if(Wu)return Ct;Wu=1,Ct=e,e.sync=u;var t=ie;function e(r,i,D){t.stat(r,function(o,a){D(o,o?!1:n(a,i))})}function u(r,i){return n(t.statSync(r),i)}function n(r,i){return r.isFile()&&s(r,i)}function s(r,i){var D=r.mode,o=r.uid,a=r.gid,c=i.uid!==void 0?i.uid:process.getuid&&process.getuid(),f=i.gid!==void 0?i.gid:process.getgid&&process.getgid(),l=parseInt("100",8),p=parseInt("010",8),C=parseInt("001",8),F=l|p,A=D&C||D&p&&a===f||D&l&&o===c||D&F&&c===0;return A}return Ct}var We;process.platform==="win32"||Xr.TESTING_WINDOWS?We=Qr():We=Zr();var Jr=Ft;Ft.sync=ei;function Ft(t,e,u){if(typeof e=="function"&&(u=e,e={}),!u){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,s){Ft(t,e||{},function(r,i){r?s(r):n(i)})})}We(t,e||{},function(n,s){n&&(n.code==="EACCES"||e&&e.ignoreErrors)&&(n=null,s=!1),u(n,s)})}function ei(t,e){try{return We.sync(t,e||{})}catch(u){if(e&&e.ignoreErrors||u.code==="EACCES")return!1;throw u}}const he=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Gu=K,ti=he?";":":",ju=Jr,Uu=t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),Ku=(t,e)=>{const u=e.colon||ti,n=t.match(/\//)||he&&t.match(/\\/)?[""]:[...he?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(u)],s=he?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",r=he?s.split(u):[""];return he&&t.indexOf(".")!==-1&&r[0]!==""&&r.unshift(""),{pathEnv:n,pathExt:r,pathExtExe:s}},Vu=(t,e,u)=>{typeof e=="function"&&(u=e,e={}),e||(e={});const{pathEnv:n,pathExt:s,pathExtExe:r}=Ku(t,e),i=[],D=a=>new Promise((c,f)=>{if(a===n.length)return e.all&&i.length?c(i):f(Uu(t));const l=n[a],p=/^".*"$/.test(l)?l.slice(1,-1):l,C=Gu.join(p,t),F=!p&&/^\.[\\\/]/.test(t)?t.slice(0,2)+C:C;c(o(F,a,0))}),o=(a,c,f)=>new Promise((l,p)=>{if(f===s.length)return l(D(c+1));const C=s[f];ju(a+C,{pathExt:r},(F,A)=>{if(!F&&A)if(e.all)i.push(a+C);else return l(a+C);return l(o(a,c,f+1))})});return u?D(0).then(a=>u(null,a),u):D(0)},ui=(t,e)=>{e=e||{};const{pathEnv:u,pathExt:n,pathExtExe:s}=Ku(t,e),r=[];for(let i=0;i<u.length;i++){const D=u[i],o=/^".*"$/.test(D)?D.slice(1,-1):D,a=Gu.join(o,t),c=!o&&/^\.[\\\/]/.test(t)?t.slice(0,2)+a:a;for(let f=0;f<n.length;f++){const l=c+n[f];try{if(ju.sync(l,{pathExt:s}))if(e.all)r.push(l);else return l}catch{}}}if(e.all&&r.length)return r;if(e.nothrow)return null;throw Uu(t)};var ni=Vu;Vu.sync=ui;var gt={exports:{}};const zu=(t={})=>{const e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};gt.exports=zu,gt.exports.default=zu;const Yu=K,si=ni,ri=gt.exports;function qu(t,e){const u=t.options.env||process.env,n=process.cwd(),s=t.options.cwd!=null,r=s&&process.chdir!==void 0&&!process.chdir.disabled;if(r)try{process.chdir(t.options.cwd)}catch{}let i;try{i=si.sync(t.command,{path:u[ri({env:u})],pathExt:e?Yu.delimiter:void 0})}catch{}finally{r&&process.chdir(n)}return i&&(i=Yu.resolve(s?t.options.cwd:"",i)),i}function ii(t){return qu(t)||qu(t,!0)}var Di=ii,mt={};const _t=/([()\][%!^"`<>&|;, *?])/g;function oi(t){return t=t.replace(_t,"^$1"),t}function ai(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace(_t,"^$1"),e&&(t=t.replace(_t,"^$1")),t}mt.command=oi,mt.argument=ai;var li=/^#!(.*)/;const ci=li;var fi=(t="")=>{const e=t.match(ci);if(!e)return null;const[u,n]=e[0].replace(/#! ?/,"").split(" "),s=u.split("/").pop();return s==="env"?n:n?`${s} ${n}`:s};const At=ie,hi=fi;function di(t){const u=Buffer.alloc(150);let n;try{n=At.openSync(t,"r"),At.readSync(n,u,0,150,0),At.closeSync(n)}catch{}return hi(u.toString())}var Ei=di;const pi=K,Xu=Di,Qu=mt,Ci=Ei,Fi=process.platform==="win32",gi=/\.(?:com|exe)$/i,mi=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function _i(t){t.file=Xu(t);const e=t.file&&Ci(t.file);return e?(t.args.unshift(t.file),t.command=e,Xu(t)):t.file}function Ai(t){if(!Fi)return t;const e=_i(t),u=!gi.test(e);if(t.options.forceShell||u){const n=mi.test(e);t.command=pi.normalize(t.command),t.command=Qu.command(t.command),t.args=t.args.map(r=>Qu.argument(r,n));const s=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${s}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}function yi(t,e,u){e&&!Array.isArray(e)&&(u=e,e=null),e=e?e.slice(0):[],u=Object.assign({},u);const n={command:t,args:e,options:u,file:void 0,original:{command:t,args:e}};return u.shell?n:Ai(n)}var wi=yi;const yt=process.platform==="win32";function wt(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}function Ri(t,e){if(!yt)return;const u=t.emit;t.emit=function(n,s){if(n==="exit"){const r=Zu(s,e);if(r)return u.call(t,"error",r)}return u.apply(t,arguments)}}function Zu(t,e){return yt&&t===1&&!e.file?wt(e.original,"spawn"):null}function bi(t,e){return yt&&t===1&&!e.file?wt(e.original,"spawnSync"):null}var vi={hookChildProcess:Ri,verifyENOENT:Zu,verifyENOENTSync:bi,notFoundError:wt};const Ju=vs,Rt=wi,bt=vi;function en(t,e,u){const n=Rt(t,e,u),s=Ju.spawn(n.command,n.args,n.options);return bt.hookChildProcess(s,n),s}function Bi(t,e,u){const n=Rt(t,e,u),s=Ju.spawnSync(n.command,n.args,n.options);return s.error=s.error||bt.verifyENOENTSync(s.status,n),s}fe.exports=en,fe.exports.spawn=en,fe.exports.sync=Bi,fe.exports._parse=Rt,fe.exports._enoent=bt;function tn(t,e){const u={...process.env},n=["inherit","inherit","inherit","ipc"];return e&&(e.noCache&&(u.ESBK_DISABLE_CACHE="1"),e.tsconfigPath&&(u.ESBK_TSCONFIG_PATH=e.tsconfigPath)),fe.exports(process.execPath,["--require",Dt.resolve("./preflight.cjs"),"--loader",Rs(Dt.resolve("./loader.js")).toString(),...t],{stdio:n,env:u})}var vt={exports:{}},Ge={};const Si=K,ee="\\\\/",un=`[^${ee}]`,te="\\.",$i="\\+",Ti="\\?",je="\\/",xi="(?=.)",nn="[^/]",Bt=`(?:${je}|$)`,sn=`(?:^|${je})`,St=`${te}{1,2}${Bt}`,Oi=`(?!${te})`,Ni=`(?!${sn}${St})`,Hi=`(?!${te}{0,1}${Bt})`,Pi=`(?!${St})`,Li=`[^.${je}]`,Ii=`${nn}*?`,rn={DOT_LITERAL:te,PLUS_LITERAL:$i,QMARK_LITERAL:Ti,SLASH_LITERAL:je,ONE_CHAR:xi,QMARK:nn,END_ANCHOR:Bt,DOTS_SLASH:St,NO_DOT:Oi,NO_DOTS:Ni,NO_DOT_SLASH:Hi,NO_DOTS_SLASH:Pi,QMARK_NO_DOT:Li,STAR:Ii,START_ANCHOR:sn},ki={...rn,SLASH_LITERAL:`[${ee}]`,QMARK:un,STAR:`${un}*?`,DOTS_SLASH:`${te}{1,2}(?:[${ee}]|$)`,NO_DOT:`(?!${te})`,NO_DOTS:`(?!(?:^|[${ee}])${te}{1,2}(?:[${ee}]|$))`,NO_DOT_SLASH:`(?!${te}{0,1}(?:[${ee}]|$))`,NO_DOTS_SLASH:`(?!${te}{1,2}(?:[${ee}]|$))`,QMARK_NO_DOT:`[^.${ee}]`,START_ANCHOR:`(?:^|[${ee}])`,END_ANCHOR:`(?:[${ee}]|$)`},Mi={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var Ue={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:Mi,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:Si.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===!0?ki:rn}};(function(t){const e=K,u=process.platform==="win32",{REGEX_BACKSLASH:n,REGEX_REMOVE_BACKSLASH:s,REGEX_SPECIAL_CHARS:r,REGEX_SPECIAL_CHARS_GLOBAL:i}=Ue;t.isObject=D=>D!==null&&typeof D=="object"&&!Array.isArray(D),t.hasRegexChars=D=>r.test(D),t.isRegexChar=D=>D.length===1&&t.hasRegexChars(D),t.escapeRegex=D=>D.replace(i,"\\$1"),t.toPosixSlashes=D=>D.replace(n,"/"),t.removeBackslashes=D=>D.replace(s,o=>o==="\\"?"":o),t.supportsLookbehinds=()=>{const D=process.version.slice(1).split(".").map(Number);return D.length===3&&D[0]>=9||D[0]===8&&D[1]>=10},t.isWindows=D=>D&&typeof D.windows=="boolean"?D.windows:u===!0||e.sep==="\\",t.escapeLast=(D,o,a)=>{const c=D.lastIndexOf(o,a);return c===-1?D:D[c-1]==="\\"?t.escapeLast(D,o,c-1):`${D.slice(0,c)}\\${D.slice(c)}`},t.removePrefix=(D,o={})=>{let a=D;return a.startsWith("./")&&(a=a.slice(2),o.prefix="./"),a},t.wrapOutput=(D,o={},a={})=>{const c=a.contains?"":"^",f=a.contains?"":"$";let l=`${c}(?:${D})${f}`;return o.negated===!0&&(l=`(?:^(?!${l}).*$)`),l}})(Ge);const Dn=Ge,{CHAR_ASTERISK:$t,CHAR_AT:Wi,CHAR_BACKWARD_SLASH:ye,CHAR_COMMA:Gi,CHAR_DOT:Tt,CHAR_EXCLAMATION_MARK:xt,CHAR_FORWARD_SLASH:on,CHAR_LEFT_CURLY_BRACE:Ot,CHAR_LEFT_PARENTHESES:Nt,CHAR_LEFT_SQUARE_BRACKET:ji,CHAR_PLUS:Ui,CHAR_QUESTION_MARK:an,CHAR_RIGHT_CURLY_BRACE:Ki,CHAR_RIGHT_PARENTHESES:ln,CHAR_RIGHT_SQUARE_BRACKET:Vi}=Ue,cn=t=>t===on||t===ye,fn=t=>{t.isPrefix!==!0&&(t.depth=t.isGlobstar?1/0:1)},zi=(t,e)=>{const u=e||{},n=t.length-1,s=u.parts===!0||u.scanToEnd===!0,r=[],i=[],D=[];let o=t,a=-1,c=0,f=0,l=!1,p=!1,C=!1,F=!1,A=!1,B=!1,H=!1,S=!1,X=!1,M=!1,ne=0,W,_,b={value:"",depth:0,isGlob:!1};const k=()=>a>=n,E=()=>o.charCodeAt(a+1),x=()=>(W=_,o.charCodeAt(++a));for(;a<n;){_=x();let G;if(_===ye){H=b.backslashes=!0,_=x(),_===Ot&&(B=!0);continue}if(B===!0||_===Ot){for(ne++;k()!==!0&&(_=x());){if(_===ye){H=b.backslashes=!0,x();continue}if(_===Ot){ne++;continue}if(B!==!0&&_===Tt&&(_=x())===Tt){if(l=b.isBrace=!0,C=b.isGlob=!0,M=!0,s===!0)continue;break}if(B!==!0&&_===Gi){if(l=b.isBrace=!0,C=b.isGlob=!0,M=!0,s===!0)continue;break}if(_===Ki&&(ne--,ne===0)){B=!1,l=b.isBrace=!0,M=!0;break}}if(s===!0)continue;break}if(_===on){if(r.push(a),i.push(b),b={value:"",depth:0,isGlob:!1},M===!0)continue;if(W===Tt&&a===c+1){c+=2;continue}f=a+1;continue}if(u.noext!==!0&&(_===Ui||_===Wi||_===$t||_===an||_===xt)===!0&&E()===Nt){if(C=b.isGlob=!0,F=b.isExtglob=!0,M=!0,_===xt&&a===c&&(X=!0),s===!0){for(;k()!==!0&&(_=x());){if(_===ye){H=b.backslashes=!0,_=x();continue}if(_===ln){C=b.isGlob=!0,M=!0;break}}continue}break}if(_===$t){if(W===$t&&(A=b.isGlobstar=!0),C=b.isGlob=!0,M=!0,s===!0)continue;break}if(_===an){if(C=b.isGlob=!0,M=!0,s===!0)continue;break}if(_===ji){for(;k()!==!0&&(G=x());){if(G===ye){H=b.backslashes=!0,x();continue}if(G===Vi){p=b.isBracket=!0,C=b.isGlob=!0,M=!0;break}}if(s===!0)continue;break}if(u.nonegate!==!0&&_===xt&&a===c){S=b.negated=!0,c++;continue}if(u.noparen!==!0&&_===Nt){if(C=b.isGlob=!0,s===!0){for(;k()!==!0&&(_=x());){if(_===Nt){H=b.backslashes=!0,_=x();continue}if(_===ln){M=!0;break}}continue}break}if(C===!0){if(M=!0,s===!0)continue;break}}u.noext===!0&&(F=!1,C=!1);let $=o,se="",h="";c>0&&(se=o.slice(0,c),o=o.slice(c),f-=c),$&&C===!0&&f>0?($=o.slice(0,f),h=o.slice(f)):C===!0?($="",h=o):$=o,$&&$!==""&&$!=="/"&&$!==o&&cn($.charCodeAt($.length-1))&&($=$.slice(0,-1)),u.unescape===!0&&(h&&(h=Dn.removeBackslashes(h)),$&&H===!0&&($=Dn.removeBackslashes($)));const d={prefix:se,input:t,start:c,base:$,glob:h,isBrace:l,isBracket:p,isGlob:C,isExtglob:F,isGlobstar:A,negated:S,negatedExtglob:X};if(u.tokens===!0&&(d.maxDepth=0,cn(_)||i.push(b),d.tokens=i),u.parts===!0||u.tokens===!0){let G;for(let R=0;R<r.length;R++){const Q=G?G+1:c,Z=r[R],V=t.slice(Q,Z);u.tokens&&(R===0&&c!==0?(i[R].isPrefix=!0,i[R].value=se):i[R].value=V,fn(i[R]),d.maxDepth+=i[R].depth),(R!==0||V!=="")&&D.push(V),G=Z}if(G&&G+1<t.length){const R=t.slice(G+1);D.push(R),u.tokens&&(i[i.length-1].value=R,fn(i[i.length-1]),d.maxDepth+=i[i.length-1].depth)}d.slashes=r,d.parts=D}return d};var Yi=zi;const Ke=Ue,z=Ge,{MAX_LENGTH:Ve,POSIX_REGEX_SOURCE:qi,REGEX_NON_SPECIAL_CHARS:Xi,REGEX_SPECIAL_CHARS_BACKREF:Qi,REPLACEMENTS:hn}=Ke,Zi=(t,e)=>{if(typeof e.expandRange=="function")return e.expandRange(...t,e);t.sort();const u=`[${t.join("-")}]`;try{new RegExp(u)}catch{return t.map(s=>z.escapeRegex(s)).join("..")}return u},de=(t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`,Ht=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");t=hn[t]||t;const u={...e},n=typeof u.maxLength=="number"?Math.min(Ve,u.maxLength):Ve;let s=t.length;if(s>n)throw new SyntaxError(`Input length: ${s}, exceeds maximum allowed length: ${n}`);const r={type:"bos",value:"",output:u.prepend||""},i=[r],D=u.capture?"":"?:",o=z.isWindows(e),a=Ke.globChars(o),c=Ke.extglobChars(a),{DOT_LITERAL:f,PLUS_LITERAL:l,SLASH_LITERAL:p,ONE_CHAR:C,DOTS_SLASH:F,NO_DOT:A,NO_DOT_SLASH:B,NO_DOTS_SLASH:H,QMARK:S,QMARK_NO_DOT:X,STAR:M,START_ANCHOR:ne}=a,W=m=>`(${D}(?:(?!${ne}${m.dot?F:f}).)*?)`,_=u.dot?"":A,b=u.dot?S:X;let k=u.bash===!0?W(u):M;u.capture&&(k=`(${k})`),typeof u.noext=="boolean"&&(u.noextglob=u.noext);const E={input:t,index:-1,start:0,dot:u.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:i};t=z.removePrefix(t,E),s=t.length;const x=[],$=[],se=[];let h=r,d;const G=()=>E.index===s-1,R=E.peek=(m=1)=>t[E.index+m],Q=E.advance=()=>t[++E.index]||"",Z=()=>t.slice(E.index+1),V=(m="",T=0)=>{E.consumed+=m,E.index+=T},Oe=m=>{E.output+=m.output!=null?m.output:m.value,V(m.value)},_s=()=>{let m=1;for(;R()==="!"&&(R(2)!=="("||R(3)==="?");)Q(),E.start++,m++;return m%2===0?!1:(E.negated=!0,E.start++,!0)},Ne=m=>{E[m]++,se.push(m)},re=m=>{E[m]--,se.pop()},w=m=>{if(h.type==="globstar"){const T=E.braces>0&&(m.type==="comma"||m.type==="brace"),g=m.extglob===!0||x.length&&(m.type==="pipe"||m.type==="paren");m.type!=="slash"&&m.type!=="paren"&&!T&&!g&&(E.output=E.output.slice(0,-h.output.length),h.type="star",h.value="*",h.output=k,E.output+=h.output)}if(x.length&&m.type!=="paren"&&(x[x.length-1].inner+=m.value),(m.value||m.output)&&Oe(m),h&&h.type==="text"&&m.type==="text"){h.value+=m.value,h.output=(h.output||"")+m.value;return}m.prev=h,i.push(m),h=m},He=(m,T)=>{const g={...c[T],conditions:1,inner:""};g.prev=h,g.parens=E.parens,g.output=E.output;const y=(u.capture?"(":"")+g.open;Ne("parens"),w({type:m,value:T,output:E.output?"":C}),w({type:"paren",extglob:!0,value:Q(),output:y}),x.push(g)},As=m=>{let T=m.close+(u.capture?")":""),g;if(m.type==="negate"){let y=k;if(m.inner&&m.inner.length>1&&m.inner.includes("/")&&(y=W(u)),(y!==k||G()||/^\)+$/.test(Z()))&&(T=m.close=`)$))${y}`),m.inner.includes("*")&&(g=Z())&&/^\.[^\\/.]+$/.test(g)){const O=Ht(g,{...e,fastpaths:!1}).output;T=m.close=`)${O})${y})`}m.prev.type==="bos"&&(E.negatedExtglob=!0)}w({type:"paren",extglob:!0,value:d,output:T}),re("parens")};if(u.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(t)){let m=!1,T=t.replace(Qi,(g,y,O,j,P,it)=>j==="\\"?(m=!0,g):j==="?"?y?y+j+(P?S.repeat(P.length):""):it===0?b+(P?S.repeat(P.length):""):S.repeat(O.length):j==="."?f.repeat(O.length):j==="*"?y?y+j+(P?k:""):k:y?g:`\\${g}`);return m===!0&&(u.unescape===!0?T=T.replace(/\\/g,""):T=T.replace(/\\+/g,g=>g.length%2===0?"\\\\":g?"\\":"")),T===t&&u.contains===!0?(E.output=t,E):(E.output=z.wrapOutput(T,E,e),E)}for(;!G();){if(d=Q(),d==="\0")continue;if(d==="\\"){const g=R();if(g==="/"&&u.bash!==!0||g==="."||g===";")continue;if(!g){d+="\\",w({type:"text",value:d});continue}const y=/^\\+/.exec(Z());let O=0;if(y&&y[0].length>2&&(O=y[0].length,E.index+=O,O%2!==0&&(d+="\\")),u.unescape===!0?d=Q():d+=Q(),E.brackets===0){w({type:"text",value:d});continue}}if(E.brackets>0&&(d!=="]"||h.value==="["||h.value==="[^")){if(u.posix!==!1&&d===":"){const g=h.value.slice(1);if(g.includes("[")&&(h.posix=!0,g.includes(":"))){const y=h.value.lastIndexOf("["),O=h.value.slice(0,y),j=h.value.slice(y+2),P=qi[j];if(P){h.value=O+P,E.backtrack=!0,Q(),!r.output&&i.indexOf(h)===1&&(r.output=C);continue}}}(d==="["&&R()!==":"||d==="-"&&R()==="]")&&(d=`\\${d}`),d==="]"&&(h.value==="["||h.value==="[^")&&(d=`\\${d}`),u.posix===!0&&d==="!"&&h.value==="["&&(d="^"),h.value+=d,Oe({value:d});continue}if(E.quotes===1&&d!=='"'){d=z.escapeRegex(d),h.value+=d,Oe({value:d});continue}if(d==='"'){E.quotes=E.quotes===1?0:1,u.keepQuotes===!0&&w({type:"text",value:d});continue}if(d==="("){Ne("parens"),w({type:"paren",value:d});continue}if(d===")"){if(E.parens===0&&u.strictBrackets===!0)throw new SyntaxError(de("opening","("));const g=x[x.length-1];if(g&&E.parens===g.parens+1){As(x.pop());continue}w({type:"paren",value:d,output:E.parens?")":"\\)"}),re("parens");continue}if(d==="["){if(u.nobracket===!0||!Z().includes("]")){if(u.nobracket!==!0&&u.strictBrackets===!0)throw new SyntaxError(de("closing","]"));d=`\\${d}`}else Ne("brackets");w({type:"bracket",value:d});continue}if(d==="]"){if(u.nobracket===!0||h&&h.type==="bracket"&&h.value.length===1){w({type:"text",value:d,output:`\\${d}`});continue}if(E.brackets===0){if(u.strictBrackets===!0)throw new SyntaxError(de("opening","["));w({type:"text",value:d,output:`\\${d}`});continue}re("brackets");const g=h.value.slice(1);if(h.posix!==!0&&g[0]==="^"&&!g.includes("/")&&(d=`/${d}`),h.value+=d,Oe({value:d}),u.literalBrackets===!1||z.hasRegexChars(g))continue;const y=z.escapeRegex(h.value);if(E.output=E.output.slice(0,-h.value.length),u.literalBrackets===!0){E.output+=y,h.value=y;continue}h.value=`(${D}${y}|${h.value})`,E.output+=h.value;continue}if(d==="{"&&u.nobrace!==!0){Ne("braces");const g={type:"brace",value:d,output:"(",outputIndex:E.output.length,tokensIndex:E.tokens.length};$.push(g),w(g);continue}if(d==="}"){const g=$[$.length-1];if(u.nobrace===!0||!g){w({type:"text",value:d,output:d});continue}let y=")";if(g.dots===!0){const O=i.slice(),j=[];for(let P=O.length-1;P>=0&&(i.pop(),O[P].type!=="brace");P--)O[P].type!=="dots"&&j.unshift(O[P].value);y=Zi(j,u),E.backtrack=!0}if(g.comma!==!0&&g.dots!==!0){const O=E.output.slice(0,g.outputIndex),j=E.tokens.slice(g.tokensIndex);g.value=g.output="\\{",d=y="\\}",E.output=O;for(const P of j)E.output+=P.output||P.value}w({type:"brace",value:d,output:y}),re("braces"),$.pop();continue}if(d==="|"){x.length>0&&x[x.length-1].conditions++,w({type:"text",value:d});continue}if(d===","){let g=d;const y=$[$.length-1];y&&se[se.length-1]==="braces"&&(y.comma=!0,g="|"),w({type:"comma",value:d,output:g});continue}if(d==="/"){if(h.type==="dot"&&E.index===E.start+1){E.start=E.index+1,E.consumed="",E.output="",i.pop(),h=r;continue}w({type:"slash",value:d,output:p});continue}if(d==="."){if(E.braces>0&&h.type==="dot"){h.value==="."&&(h.output=f);const g=$[$.length-1];h.type="dots",h.output+=d,h.value+=d,g.dots=!0;continue}if(E.braces+E.parens===0&&h.type!=="bos"&&h.type!=="slash"){w({type:"text",value:d,output:f});continue}w({type:"dot",value:d,output:f});continue}if(d==="?"){if(!(h&&h.value==="(")&&u.noextglob!==!0&&R()==="("&&R(2)!=="?"){He("qmark",d);continue}if(h&&h.type==="paren"){const y=R();let O=d;if(y==="<"&&!z.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(h.value==="("&&!/[!=<:]/.test(y)||y==="<"&&!/<([!=]|\w+>)/.test(Z()))&&(O=`\\${d}`),w({type:"text",value:d,output:O});continue}if(u.dot!==!0&&(h.type==="slash"||h.type==="bos")){w({type:"qmark",value:d,output:X});continue}w({type:"qmark",value:d,output:S});continue}if(d==="!"){if(u.noextglob!==!0&&R()==="("&&(R(2)!=="?"||!/[!=<:]/.test(R(3)))){He("negate",d);continue}if(u.nonegate!==!0&&E.index===0){_s();continue}}if(d==="+"){if(u.noextglob!==!0&&R()==="("&&R(2)!=="?"){He("plus",d);continue}if(h&&h.value==="("||u.regex===!1){w({type:"plus",value:d,output:l});continue}if(h&&(h.type==="bracket"||h.type==="paren"||h.type==="brace")||E.parens>0){w({type:"plus",value:d});continue}w({type:"plus",value:l});continue}if(d==="@"){if(u.noextglob!==!0&&R()==="("&&R(2)!=="?"){w({type:"at",extglob:!0,value:d,output:""});continue}w({type:"text",value:d});continue}if(d!=="*"){(d==="$"||d==="^")&&(d=`\\${d}`);const g=Xi.exec(Z());g&&(d+=g[0],E.index+=g[0].length),w({type:"text",value:d});continue}if(h&&(h.type==="globstar"||h.star===!0)){h.type="star",h.star=!0,h.value+=d,h.output=k,E.backtrack=!0,E.globstar=!0,V(d);continue}let m=Z();if(u.noextglob!==!0&&/^\([^?]/.test(m)){He("star",d);continue}if(h.type==="star"){if(u.noglobstar===!0){V(d);continue}const g=h.prev,y=g.prev,O=g.type==="slash"||g.type==="bos",j=y&&(y.type==="star"||y.type==="globstar");if(u.bash===!0&&(!O||m[0]&&m[0]!=="/")){w({type:"star",value:d,output:""});continue}const P=E.braces>0&&(g.type==="comma"||g.type==="brace"),it=x.length&&(g.type==="pipe"||g.type==="paren");if(!O&&g.type!=="paren"&&!P&&!it){w({type:"star",value:d,output:""});continue}for(;m.slice(0,3)==="/**";){const Pe=t[E.index+4];if(Pe&&Pe!=="/")break;m=m.slice(3),V("/**",3)}if(g.type==="bos"&&G()){h.type="globstar",h.value+=d,h.output=W(u),E.output=h.output,E.globstar=!0,V(d);continue}if(g.type==="slash"&&g.prev.type!=="bos"&&!j&&G()){E.output=E.output.slice(0,-(g.output+h.output).length),g.output=`(?:${g.output}`,h.type="globstar",h.output=W(u)+(u.strictSlashes?")":"|$)"),h.value+=d,E.globstar=!0,E.output+=g.output+h.output,V(d);continue}if(g.type==="slash"&&g.prev.type!=="bos"&&m[0]==="/"){const Pe=m[1]!==void 0?"|$":"";E.output=E.output.slice(0,-(g.output+h.output).length),g.output=`(?:${g.output}`,h.type="globstar",h.output=`${W(u)}${p}|${p}${Pe})`,h.value+=d,E.output+=g.output+h.output,E.globstar=!0,V(d+Q()),w({type:"slash",value:"/",output:""});continue}if(g.type==="bos"&&m[0]==="/"){h.type="globstar",h.value+=d,h.output=`(?:^|${p}|${W(u)}${p})`,E.output=h.output,E.globstar=!0,V(d+Q()),w({type:"slash",value:"/",output:""});continue}E.output=E.output.slice(0,-h.output.length),h.type="globstar",h.output=W(u),h.value+=d,E.output+=h.output,E.globstar=!0,V(d);continue}const T={type:"star",value:d,output:k};if(u.bash===!0){T.output=".*?",(h.type==="bos"||h.type==="slash")&&(T.output=_+T.output),w(T);continue}if(h&&(h.type==="bracket"||h.type==="paren")&&u.regex===!0){T.output=d,w(T);continue}(E.index===E.start||h.type==="slash"||h.type==="dot")&&(h.type==="dot"?(E.output+=B,h.output+=B):u.dot===!0?(E.output+=H,h.output+=H):(E.output+=_,h.output+=_),R()!=="*"&&(E.output+=C,h.output+=C)),w(T)}for(;E.brackets>0;){if(u.strictBrackets===!0)throw new SyntaxError(de("closing","]"));E.output=z.escapeLast(E.output,"["),re("brackets")}for(;E.parens>0;){if(u.strictBrackets===!0)throw new SyntaxError(de("closing",")"));E.output=z.escapeLast(E.output,"("),re("parens")}for(;E.braces>0;){if(u.strictBrackets===!0)throw new SyntaxError(de("closing","}"));E.output=z.escapeLast(E.output,"{"),re("braces")}if(u.strictSlashes!==!0&&(h.type==="star"||h.type==="bracket")&&w({type:"maybe_slash",value:"",output:`${p}?`}),E.backtrack===!0){E.output="";for(const m of E.tokens)E.output+=m.output!=null?m.output:m.value,m.suffix&&(E.output+=m.suffix)}return E};Ht.fastpaths=(t,e)=>{const u={...e},n=typeof u.maxLength=="number"?Math.min(Ve,u.maxLength):Ve,s=t.length;if(s>n)throw new SyntaxError(`Input length: ${s}, exceeds maximum allowed length: ${n}`);t=hn[t]||t;const r=z.isWindows(e),{DOT_LITERAL:i,SLASH_LITERAL:D,ONE_CHAR:o,DOTS_SLASH:a,NO_DOT:c,NO_DOTS:f,NO_DOTS_SLASH:l,STAR:p,START_ANCHOR:C}=Ke.globChars(r),F=u.dot?f:c,A=u.dot?l:c,B=u.capture?"":"?:",H={negated:!1,prefix:""};let S=u.bash===!0?".*?":p;u.capture&&(S=`(${S})`);const X=_=>_.noglobstar===!0?S:`(${B}(?:(?!${C}${_.dot?a:i}).)*?)`,M=_=>{switch(_){case"*":return`${F}${o}${S}`;case".*":return`${i}${o}${S}`;case"*.*":return`${F}${S}${i}${o}${S}`;case"*/*":return`${F}${S}${D}${o}${A}${S}`;case"**":return F+X(u);case"**/*":return`(?:${F}${X(u)}${D})?${A}${o}${S}`;case"**/*.*":return`(?:${F}${X(u)}${D})?${A}${S}${i}${o}${S}`;case"**/.*":return`(?:${F}${X(u)}${D})?${i}${o}${S}`;default:{const b=/^(.*?)\.(\w+)$/.exec(_);if(!b)return;const k=M(b[1]);return k?k+i+b[2]:void 0}}},ne=z.removePrefix(t,H);let W=M(ne);return W&&u.strictSlashes!==!0&&(W+=`${D}?`),W};var Ji=Ht;const eD=K,tD=Yi,Pt=Ji,Lt=Ge,uD=Ue,nD=t=>t&&typeof t=="object"&&!Array.isArray(t),N=(t,e,u=!1)=>{if(Array.isArray(t)){const c=t.map(l=>N(l,e,u));return l=>{for(const p of c){const C=p(l);if(C)return C}return!1}}const n=nD(t)&&t.tokens&&t.input;if(t===""||typeof t!="string"&&!n)throw new TypeError("Expected pattern to be a non-empty string");const s=e||{},r=Lt.isWindows(e),i=n?N.compileRe(t,e):N.makeRe(t,e,!1,!0),D=i.state;delete i.state;let o=()=>!1;if(s.ignore){const c={...e,ignore:null,onMatch:null,onResult:null};o=N(s.ignore,c,u)}const a=(c,f=!1)=>{const{isMatch:l,match:p,output:C}=N.test(c,i,e,{glob:t,posix:r}),F={glob:t,state:D,regex:i,posix:r,input:c,output:C,match:p,isMatch:l};return typeof s.onResult=="function"&&s.onResult(F),l===!1?(F.isMatch=!1,f?F:!1):o(c)?(typeof s.onIgnore=="function"&&s.onIgnore(F),F.isMatch=!1,f?F:!1):(typeof s.onMatch=="function"&&s.onMatch(F),f?F:!0)};return u&&(a.state=D),a};N.test=(t,e,u,{glob:n,posix:s}={})=>{if(typeof t!="string")throw new TypeError("Expected input to be a string");if(t==="")return{isMatch:!1,output:""};const r=u||{},i=r.format||(s?Lt.toPosixSlashes:null);let D=t===n,o=D&&i?i(t):t;return D===!1&&(o=i?i(t):t,D=o===n),(D===!1||r.capture===!0)&&(r.matchBase===!0||r.basename===!0?D=N.matchBase(t,e,u,s):D=e.exec(o)),{isMatch:Boolean(D),match:D,output:o}},N.matchBase=(t,e,u,n=Lt.isWindows(u))=>(e instanceof RegExp?e:N.makeRe(e,u)).test(eD.basename(t)),N.isMatch=(t,e,u)=>N(e,u)(t),N.parse=(t,e)=>Array.isArray(t)?t.map(u=>N.parse(u,e)):Pt(t,{...e,fastpaths:!1}),N.scan=(t,e)=>tD(t,e),N.compileRe=(t,e,u=!1,n=!1)=>{if(u===!0)return t.output;const s=e||{},r=s.contains?"":"^",i=s.contains?"":"$";let D=`${r}(?:${t.output})${i}`;t&&t.negated===!0&&(D=`^(?!${D}).*$`);const o=N.toRegex(D,e);return n===!0&&(o.state=t),o},N.makeRe=(t,e={},u=!1,n=!1)=>{if(!t||typeof t!="string")throw new TypeError("Expected a non-empty string");let s={negated:!1,fastpaths:!0};return e.fastpaths!==!1&&(t[0]==="."||t[0]==="*")&&(s.output=Pt.fastpaths(t,e)),s.output||(s=Pt(t,e)),N.compileRe(s,e,u,n)},N.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?"i":""))}catch(u){if(e&&e.debug===!0)throw u;return/$^/}},N.constants=uD;var sD=N;(function(t){t.exports=sD})(vt);const we=ie,{Readable:rD}=$s,Re=K,{promisify:ze}=me,It=vt.exports,iD=ze(we.readdir),DD=ze(we.stat),dn=ze(we.lstat),oD=ze(we.realpath),aD="!",En="READDIRP_RECURSIVE_ERROR",lD=new Set(["ENOENT","EPERM","EACCES","ELOOP",En]),kt="files",pn="directories",Ye="files_directories",qe="all",Cn=[kt,pn,Ye,qe],cD=t=>lD.has(t.code),[Fn,fD]=process.versions.node.split(".").slice(0,2).map(t=>Number.parseInt(t,10)),hD=process.platform==="win32"&&(Fn>10||Fn===10&&fD>=5),gn=t=>{if(t!==void 0){if(typeof t=="function")return t;if(typeof t=="string"){const e=It(t.trim());return u=>e(u.basename)}if(Array.isArray(t)){const e=[],u=[];for(const n of t){const s=n.trim();s.charAt(0)===aD?u.push(It(s.slice(1))):e.push(It(s))}return u.length>0?e.length>0?n=>e.some(s=>s(n.basename))&&!u.some(s=>s(n.basename)):n=>!u.some(s=>s(n.basename)):n=>e.some(s=>s(n.basename))}}};class rt extends rD{static get defaultOptions(){return{root:".",fileFilter:e=>!0,directoryFilter:e=>!0,type:kt,lstat:!1,depth:2147483648,alwaysStat:!1}}constructor(e={}){super({objectMode:!0,autoDestroy:!0,highWaterMark:e.highWaterMark||4096});const u={...rt.defaultOptions,...e},{root:n,type:s}=u;this._fileFilter=gn(u.fileFilter),this._directoryFilter=gn(u.directoryFilter);const r=u.lstat?dn:DD;hD?this._stat=i=>r(i,{bigint:!0}):this._stat=r,this._maxDepth=u.depth,this._wantsDir=[pn,Ye,qe].includes(s),this._wantsFile=[kt,Ye,qe].includes(s),this._wantsEverything=s===qe,this._root=Re.resolve(n),this._isDirent="Dirent"in we&&!u.alwaysStat,this._statsProp=this._isDirent?"dirent":"stats",this._rdOptions={encoding:"utf8",withFileTypes:this._isDirent},this.parents=[this._exploreDir(n,1)],this.reading=!1,this.parent=void 0}async _read(e){if(!this.reading){this.reading=!0;try{for(;!this.destroyed&&e>0;){const{path:u,depth:n,files:s=[]}=this.parent||{};if(s.length>0){const r=s.splice(0,e).map(i=>this._formatEntry(i,u));for(const i of await Promise.all(r)){if(this.destroyed)return;const D=await this._getEntryType(i);D==="directory"&&this._directoryFilter(i)?(n<=this._maxDepth&&this.parents.push(this._exploreDir(i.fullPath,n+1)),this._wantsDir&&(this.push(i),e--)):(D==="file"||this._includeAsFile(i))&&this._fileFilter(i)&&this._wantsFile&&(this.push(i),e--)}}else{const r=this.parents.pop();if(!r){this.push(null);break}if(this.parent=await r,this.destroyed)return}}}catch(u){this.destroy(u)}finally{this.reading=!1}}}async _exploreDir(e,u){let n;try{n=await iD(e,this._rdOptions)}catch(s){this._onError(s)}return{files:n,depth:u,path:e}}async _formatEntry(e,u){let n;try{const s=this._isDirent?e.name:e,r=Re.resolve(Re.join(u,s));n={path:Re.relative(this._root,r),fullPath:r,basename:s},n[this._statsProp]=this._isDirent?e:await this._stat(r)}catch(s){this._onError(s)}return n}_onError(e){cD(e)&&!this.destroyed?this.emit("warn",e):this.destroy(e)}async _getEntryType(e){const u=e&&e[this._statsProp];if(!!u){if(u.isFile())return"file";if(u.isDirectory())return"directory";if(u&&u.isSymbolicLink()){const n=e.fullPath;try{const s=await oD(n),r=await dn(s);if(r.isFile())return"file";if(r.isDirectory()){const i=s.length;if(n.startsWith(s)&&n.substr(i,1)===Re.sep){const D=new Error(`Circular symlink detected: "${n}" points to "${s}"`);return D.code=En,this._onError(D)}return"directory"}}catch(s){this._onError(s)}}}}_includeAsFile(e){const u=e&&e[this._statsProp];return u&&this._wantsEverything&&!u.isDirectory()}}const Ee=(t,e={})=>{let u=e.entryType||e.type;if(u==="both"&&(u=Ye),u&&(e.type=u),t){if(typeof t!="string")throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");if(u&&!Cn.includes(u))throw new Error(`readdirp: Invalid type passed. Use one of ${Cn.join(", ")}`)}else throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");return e.root=t,new rt(e)},dD=(t,e={})=>new Promise((u,n)=>{const s=[];Ee(t,e).on("data",r=>s.push(r)).on("end",()=>u(s)).on("error",r=>n(r))});Ee.promise=dD,Ee.ReaddirpStream=rt,Ee.default=Ee;var ED=Ee,Mt={exports:{}};/*!
 * normalize-path <https://github.com/jonschlinkert/normalize-path>
 *
 * Copyright (c) 2014-2018, Jon Schlinkert.
 * Released under the MIT License.
 */var mn=function(t,e){if(typeof t!="string")throw new TypeError("expected path to be a string");if(t==="\\"||t==="/")return"/";var u=t.length;if(u<=1)return t;var n="";if(u>4&&t[3]==="\\"){var s=t[2];(s==="?"||s===".")&&t.slice(0,2)==="\\\\"&&(t=t.slice(2),n="//")}var r=t.split(/[/\\]+/);return e!==!1&&r[r.length-1]===""&&r.pop(),n+r.join("/")};Object.defineProperty(Mt.exports,"__esModule",{value:!0});const _n=vt.exports,pD=mn,An="!",CD={returnIndex:!1},FD=t=>Array.isArray(t)?t:[t],gD=(t,e)=>{if(typeof t=="function")return t;if(typeof t=="string"){const u=_n(t,e);return n=>t===n||u(n)}return t instanceof RegExp?u=>t.test(u):u=>!1},yn=(t,e,u,n)=>{const s=Array.isArray(u),r=s?u[0]:u;if(!s&&typeof r!="string")throw new TypeError("anymatch: second argument must be a string: got "+Object.prototype.toString.call(r));const i=pD(r);for(let o=0;o<e.length;o++){const a=e[o];if(a(i))return n?-1:!1}const D=s&&[i].concat(u.slice(1));for(let o=0;o<t.length;o++){const a=t[o];if(s?a(...D):a(i))return n?o:!0}return n?-1:!1},Wt=(t,e,u=CD)=>{if(t==null)throw new TypeError("anymatch: specify first argument");const n=typeof u=="boolean"?{returnIndex:u}:u,s=n.returnIndex||!1,r=FD(t),i=r.filter(o=>typeof o=="string"&&o.charAt(0)===An).map(o=>o.slice(1)).map(o=>_n(o,n)),D=r.filter(o=>typeof o!="string"||typeof o=="string"&&o.charAt(0)!==An).map(o=>gD(o,n));return e==null?(o,a=!1)=>yn(D,i,o,typeof a=="boolean"?a:!1):yn(D,i,e,s)};Wt.default=Wt,Mt.exports=Wt;/*!
 * is-extglob <https://github.com/jonschlinkert/is-extglob>
 *
 * Copyright (c) 2014-2016, Jon Schlinkert.
 * Licensed under the MIT License.
 */var mD=function(e){if(typeof e!="string"||e==="")return!1;for(var u;u=/(\\).|([@?!+*]\(.*\))/g.exec(e);){if(u[2])return!0;e=e.slice(u.index+u[0].length)}return!1};/*!
 * is-glob <https://github.com/jonschlinkert/is-glob>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var _D=mD,wn={"{":"}","(":")","[":"]"},AD=function(t){if(t[0]==="!")return!0;for(var e=0,u=-2,n=-2,s=-2,r=-2,i=-2;e<t.length;){if(t[e]==="*"||t[e+1]==="?"&&/[\].+)]/.test(t[e])||n!==-1&&t[e]==="["&&t[e+1]!=="]"&&(n<e&&(n=t.indexOf("]",e)),n>e&&(i===-1||i>n||(i=t.indexOf("\\",e),i===-1||i>n)))||s!==-1&&t[e]==="{"&&t[e+1]!=="}"&&(s=t.indexOf("}",e),s>e&&(i=t.indexOf("\\",e),i===-1||i>s))||r!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"&&(r=t.indexOf(")",e),r>e&&(i=t.indexOf("\\",e),i===-1||i>r))||u!==-1&&t[e]==="("&&t[e+1]!=="|"&&(u<e&&(u=t.indexOf("|",e)),u!==-1&&t[u+1]!==")"&&(r=t.indexOf(")",u),r>u&&(i=t.indexOf("\\",u),i===-1||i>r))))return!0;if(t[e]==="\\"){var D=t[e+1];e+=2;var o=wn[D];if(o){var a=t.indexOf(o,e);a!==-1&&(e=a+1)}if(t[e]==="!")return!0}else e++}return!1},yD=function(t){if(t[0]==="!")return!0;for(var e=0;e<t.length;){if(/[*?{}()[\]]/.test(t[e]))return!0;if(t[e]==="\\"){var u=t[e+1];e+=2;var n=wn[u];if(n){var s=t.indexOf(n,e);s!==-1&&(e=s+1)}if(t[e]==="!")return!0}else e++}return!1},Rn=function(e,u){if(typeof e!="string"||e==="")return!1;if(_D(e))return!0;var n=AD;return u&&u.strict===!1&&(n=yD),n(e)},wD=Rn,RD=K.posix.dirname,bD=Fu.platform()==="win32",Gt="/",vD=/\\/g,BD=/[\{\[].*[\}\]]$/,SD=/(^|[^\\])([\{\[]|\([^\)]+$)/,$D=/\\([\!\*\?\|\[\]\(\)\{\}])/g,TD=function(e,u){var n=Object.assign({flipBackslashes:!0},u);n.flipBackslashes&&bD&&e.indexOf(Gt)<0&&(e=e.replace(vD,Gt)),BD.test(e)&&(e+=Gt),e+="a";do e=RD(e);while(wD(e)||SD.test(e));return e.replace($D,"$1")},Xe={};(function(t){t.isInteger=e=>typeof e=="number"?Number.isInteger(e):typeof e=="string"&&e.trim()!==""?Number.isInteger(Number(e)):!1,t.find=(e,u)=>e.nodes.find(n=>n.type===u),t.exceedsLimit=(e,u,n=1,s)=>s===!1||!t.isInteger(e)||!t.isInteger(u)?!1:(Number(u)-Number(e))/Number(n)>=s,t.escapeNode=(e,u=0,n)=>{let s=e.nodes[u];!s||(n&&s.type===n||s.type==="open"||s.type==="close")&&s.escaped!==!0&&(s.value="\\"+s.value,s.escaped=!0)},t.encloseBrace=e=>e.type!=="brace"?!1:e.commas>>0+e.ranges>>0===0?(e.invalid=!0,!0):!1,t.isInvalidBrace=e=>e.type!=="brace"?!1:e.invalid===!0||e.dollar?!0:e.commas>>0+e.ranges>>0===0||e.open!==!0||e.close!==!0?(e.invalid=!0,!0):!1,t.isOpenOrClose=e=>e.type==="open"||e.type==="close"?!0:e.open===!0||e.close===!0,t.reduce=e=>e.reduce((u,n)=>(n.type==="text"&&u.push(n.value),n.type==="range"&&(n.type="text"),u),[]),t.flatten=(...e)=>{const u=[],n=s=>{for(let r=0;r<s.length;r++){let i=s[r];Array.isArray(i)?n(i):i!==void 0&&u.push(i)}return u};return n(e),u}})(Xe);const bn=Xe;var jt=(t,e={})=>{let u=(n,s={})=>{let r=e.escapeInvalid&&bn.isInvalidBrace(s),i=n.invalid===!0&&e.escapeInvalid===!0,D="";if(n.value)return(r||i)&&bn.isOpenOrClose(n)?"\\"+n.value:n.value;if(n.value)return n.value;if(n.nodes)for(let o of n.nodes)D+=u(o);return D};return u(t)};/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */var xD=function(t){return typeof t=="number"?t-t===0:typeof t=="string"&&t.trim()!==""?Number.isFinite?Number.isFinite(+t):isFinite(+t):!1};/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */const vn=xD,oe=(t,e,u)=>{if(vn(t)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(e===void 0||t===e)return String(t);if(vn(e)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let n={relaxZeros:!0,...u};typeof n.strictZeros=="boolean"&&(n.relaxZeros=n.strictZeros===!1);let s=String(n.relaxZeros),r=String(n.shorthand),i=String(n.capture),D=String(n.wrap),o=t+":"+e+"="+s+r+i+D;if(oe.cache.hasOwnProperty(o))return oe.cache[o].result;let a=Math.min(t,e),c=Math.max(t,e);if(Math.abs(a-c)===1){let F=t+"|"+e;return n.capture?`(${F})`:n.wrap===!1?F:`(?:${F})`}let f=On(t)||On(e),l={min:t,max:e,a,b:c},p=[],C=[];if(f&&(l.isPadded=f,l.maxLen=String(l.max).length),a<0){let F=c<0?Math.abs(c):1;C=Bn(F,Math.abs(a),l,n),a=l.a=0}return c>=0&&(p=Bn(a,c,l,n)),l.negatives=C,l.positives=p,l.result=OD(C,p),n.capture===!0?l.result=`(${l.result})`:n.wrap!==!1&&p.length+C.length>1&&(l.result=`(?:${l.result})`),oe.cache[o]=l,l.result};function OD(t,e,u){let n=Ut(t,e,"-",!1)||[],s=Ut(e,t,"",!1)||[],r=Ut(t,e,"-?",!0)||[];return n.concat(r).concat(s).join("|")}function ND(t,e){let u=1,n=1,s=$n(t,u),r=new Set([e]);for(;t<=s&&s<=e;)r.add(s),u+=1,s=$n(t,u);for(s=Tn(e+1,n)-1;t<s&&s<=e;)r.add(s),n+=1,s=Tn(e+1,n)-1;return r=[...r],r.sort(LD),r}function HD(t,e,u){if(t===e)return{pattern:t,count:[],digits:0};let n=PD(t,e),s=n.length,r="",i=0;for(let D=0;D<s;D++){let[o,a]=n[D];o===a?r+=o:o!=="0"||a!=="9"?r+=ID(o,a):i++}return i&&(r+=u.shorthand===!0?"\\d":"[0-9]"),{pattern:r,count:[i],digits:s}}function Bn(t,e,u,n){let s=ND(t,e),r=[],i=t,D;for(let o=0;o<s.length;o++){let a=s[o],c=HD(String(i),String(a),n),f="";if(!u.isPadded&&D&&D.pattern===c.pattern){D.count.length>1&&D.count.pop(),D.count.push(c.count[0]),D.string=D.pattern+xn(D.count),i=a+1;continue}u.isPadded&&(f=kD(a,u,n)),c.string=f+c.pattern+xn(c.count),r.push(c),i=a+1,D=c}return r}function Ut(t,e,u,n,s){let r=[];for(let i of t){let{string:D}=i;!n&&!Sn(e,"string",D)&&r.push(u+D),n&&Sn(e,"string",D)&&r.push(u+D)}return r}function PD(t,e){let u=[];for(let n=0;n<t.length;n++)u.push([t[n],e[n]]);return u}function LD(t,e){return t>e?1:e>t?-1:0}function Sn(t,e,u){return t.some(n=>n[e]===u)}function $n(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}function Tn(t,e){return t-t%Math.pow(10,e)}function xn(t){let[e=0,u=""]=t;return u||e>1?`{${e+(u?","+u:"")}}`:""}function ID(t,e,u){return`[${t}${e-t===1?"":"-"}${e}]`}function On(t){return/^-?(0+)\d/.test(t)}function kD(t,e,u){if(!e.isPadded)return t;let n=Math.abs(e.maxLen-String(t).length),s=u.relaxZeros!==!1;switch(n){case 0:return"";case 1:return s?"0?":"0";case 2:return s?"0{0,2}":"00";default:return s?`0{0,${n}}`:`0{${n}}`}}oe.cache={},oe.clearCache=()=>oe.cache={};var MD=oe;/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */const WD=me,Nn=MD,Hn=t=>t!==null&&typeof t=="object"&&!Array.isArray(t),GD=t=>e=>t===!0?Number(e):String(e),Kt=t=>typeof t=="number"||typeof t=="string"&&t!=="",be=t=>Number.isInteger(+t),Vt=t=>{let e=`${t}`,u=-1;if(e[0]==="-"&&(e=e.slice(1)),e==="0")return!1;for(;e[++u]==="0";);return u>0},jD=(t,e,u)=>typeof t=="string"||typeof e=="string"?!0:u.stringify===!0,UD=(t,e,u)=>{if(e>0){let n=t[0]==="-"?"-":"";n&&(t=t.slice(1)),t=n+t.padStart(n?e-1:e,"0")}return u===!1?String(t):t},Pn=(t,e)=>{let u=t[0]==="-"?"-":"";for(u&&(t=t.slice(1),e--);t.length<e;)t="0"+t;return u?"-"+t:t},KD=(t,e)=>{t.negatives.sort((i,D)=>i<D?-1:i>D?1:0),t.positives.sort((i,D)=>i<D?-1:i>D?1:0);let u=e.capture?"":"?:",n="",s="",r;return t.positives.length&&(n=t.positives.join("|")),t.negatives.length&&(s=`-(${u}${t.negatives.join("|")})`),n&&s?r=`${n}|${s}`:r=n||s,e.wrap?`(${u}${r})`:r},Ln=(t,e,u,n)=>{if(u)return Nn(t,e,{wrap:!1,...n});let s=String.fromCharCode(t);if(t===e)return s;let r=String.fromCharCode(e);return`[${s}-${r}]`},In=(t,e,u)=>{if(Array.isArray(t)){let n=u.wrap===!0,s=u.capture?"":"?:";return n?`(${s}${t.join("|")})`:t.join("|")}return Nn(t,e,u)},kn=(...t)=>new RangeError("Invalid range arguments: "+WD.inspect(...t)),Mn=(t,e,u)=>{if(u.strictRanges===!0)throw kn([t,e]);return[]},VD=(t,e)=>{if(e.strictRanges===!0)throw new TypeError(`Expected step "${t}" to be a number`);return[]},zD=(t,e,u=1,n={})=>{let s=Number(t),r=Number(e);if(!Number.isInteger(s)||!Number.isInteger(r)){if(n.strictRanges===!0)throw kn([t,e]);return[]}s===0&&(s=0),r===0&&(r=0);let i=s>r,D=String(t),o=String(e),a=String(u);u=Math.max(Math.abs(u),1);let c=Vt(D)||Vt(o)||Vt(a),f=c?Math.max(D.length,o.length,a.length):0,l=c===!1&&jD(t,e,n)===!1,p=n.transform||GD(l);if(n.toRegex&&u===1)return Ln(Pn(t,f),Pn(e,f),!0,n);let C={negatives:[],positives:[]},F=H=>C[H<0?"negatives":"positives"].push(Math.abs(H)),A=[],B=0;for(;i?s>=r:s<=r;)n.toRegex===!0&&u>1?F(s):A.push(UD(p(s,B),f,l)),s=i?s-u:s+u,B++;return n.toRegex===!0?u>1?KD(C,n):In(A,null,{wrap:!1,...n}):A},YD=(t,e,u=1,n={})=>{if(!be(t)&&t.length>1||!be(e)&&e.length>1)return Mn(t,e,n);let s=n.transform||(l=>String.fromCharCode(l)),r=`${t}`.charCodeAt(0),i=`${e}`.charCodeAt(0),D=r>i,o=Math.min(r,i),a=Math.max(r,i);if(n.toRegex&&u===1)return Ln(o,a,!1,n);let c=[],f=0;for(;D?r>=i:r<=i;)c.push(s(r,f)),r=D?r-u:r+u,f++;return n.toRegex===!0?In(c,null,{wrap:!1,options:n}):c},Qe=(t,e,u,n={})=>{if(e==null&&Kt(t))return[t];if(!Kt(t)||!Kt(e))return Mn(t,e,n);if(typeof u=="function")return Qe(t,e,1,{transform:u});if(Hn(u))return Qe(t,e,0,u);let s={...n};return s.capture===!0&&(s.wrap=!0),u=u||s.step||1,be(u)?be(t)&&be(e)?zD(t,e,u,s):YD(t,e,Math.max(Math.abs(u),1),s):u!=null&&!Hn(u)?VD(u,s):Qe(t,e,1,u)};var Wn=Qe;const qD=Wn,Gn=Xe,XD=(t,e={})=>{let u=(n,s={})=>{let r=Gn.isInvalidBrace(s),i=n.invalid===!0&&e.escapeInvalid===!0,D=r===!0||i===!0,o=e.escapeInvalid===!0?"\\":"",a="";if(n.isOpen===!0||n.isClose===!0)return o+n.value;if(n.type==="open")return D?o+n.value:"(";if(n.type==="close")return D?o+n.value:")";if(n.type==="comma")return n.prev.type==="comma"?"":D?n.value:"|";if(n.value)return n.value;if(n.nodes&&n.ranges>0){let c=Gn.reduce(n.nodes),f=qD(...c,{...e,wrap:!1,toRegex:!0});if(f.length!==0)return c.length>1&&f.length>1?`(${f})`:f}if(n.nodes)for(let c of n.nodes)a+=u(c,n);return a};return u(t)};var QD=XD;const ZD=Wn,jn=jt,pe=Xe,ae=(t="",e="",u=!1)=>{let n=[];if(t=[].concat(t),e=[].concat(e),!e.length)return t;if(!t.length)return u?pe.flatten(e).map(s=>`{${s}}`):e;for(let s of t)if(Array.isArray(s))for(let r of s)n.push(ae(r,e,u));else for(let r of e)u===!0&&typeof r=="string"&&(r=`{${r}}`),n.push(Array.isArray(r)?ae(s,r,u):s+r);return pe.flatten(n)},JD=(t,e={})=>{let u=e.rangeLimit===void 0?1e3:e.rangeLimit,n=(s,r={})=>{s.queue=[];let i=r,D=r.queue;for(;i.type!=="brace"&&i.type!=="root"&&i.parent;)i=i.parent,D=i.queue;if(s.invalid||s.dollar){D.push(ae(D.pop(),jn(s,e)));return}if(s.type==="brace"&&s.invalid!==!0&&s.nodes.length===2){D.push(ae(D.pop(),["{}"]));return}if(s.nodes&&s.ranges>0){let f=pe.reduce(s.nodes);if(pe.exceedsLimit(...f,e.step,u))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let l=ZD(...f,e);l.length===0&&(l=jn(s,e)),D.push(ae(D.pop(),l)),s.nodes=[];return}let o=pe.encloseBrace(s),a=s.queue,c=s;for(;c.type!=="brace"&&c.type!=="root"&&c.parent;)c=c.parent,a=c.queue;for(let f=0;f<s.nodes.length;f++){let l=s.nodes[f];if(l.type==="comma"&&s.type==="brace"){f===1&&a.push(""),a.push("");continue}if(l.type==="close"){D.push(ae(D.pop(),a,o));continue}if(l.value&&l.type!=="open"){a.push(ae(a.pop(),l.value));continue}l.nodes&&n(l,s)}return a};return pe.flatten(n(t))};var eo=JD,to={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"};const uo=jt,{MAX_LENGTH:Un,CHAR_BACKSLASH:zt,CHAR_BACKTICK:no,CHAR_COMMA:so,CHAR_DOT:ro,CHAR_LEFT_PARENTHESES:io,CHAR_RIGHT_PARENTHESES:Do,CHAR_LEFT_CURLY_BRACE:oo,CHAR_RIGHT_CURLY_BRACE:ao,CHAR_LEFT_SQUARE_BRACKET:Kn,CHAR_RIGHT_SQUARE_BRACKET:Vn,CHAR_DOUBLE_QUOTE:lo,CHAR_SINGLE_QUOTE:co,CHAR_NO_BREAK_SPACE:fo,CHAR_ZERO_WIDTH_NOBREAK_SPACE:ho}=to,Eo=(t,e={})=>{if(typeof t!="string")throw new TypeError("Expected a string");let u=e||{},n=typeof u.maxLength=="number"?Math.min(Un,u.maxLength):Un;if(t.length>n)throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${n})`);let s={type:"root",input:t,nodes:[]},r=[s],i=s,D=s,o=0,a=t.length,c=0,f=0,l;const p=()=>t[c++],C=F=>{if(F.type==="text"&&D.type==="dot"&&(D.type="text"),D&&D.type==="text"&&F.type==="text"){D.value+=F.value;return}return i.nodes.push(F),F.parent=i,F.prev=D,D=F,F};for(C({type:"bos"});c<a;)if(i=r[r.length-1],l=p(),!(l===ho||l===fo)){if(l===zt){C({type:"text",value:(e.keepEscaping?l:"")+p()});continue}if(l===Vn){C({type:"text",value:"\\"+l});continue}if(l===Kn){o++;let F;for(;c<a&&(F=p());){if(l+=F,F===Kn){o++;continue}if(F===zt){l+=p();continue}if(F===Vn&&(o--,o===0))break}C({type:"text",value:l});continue}if(l===io){i=C({type:"paren",nodes:[]}),r.push(i),C({type:"text",value:l});continue}if(l===Do){if(i.type!=="paren"){C({type:"text",value:l});continue}i=r.pop(),C({type:"text",value:l}),i=r[r.length-1];continue}if(l===lo||l===co||l===no){let F=l,A;for(e.keepQuotes!==!0&&(l="");c<a&&(A=p());){if(A===zt){l+=A+p();continue}if(A===F){e.keepQuotes===!0&&(l+=A);break}l+=A}C({type:"text",value:l});continue}if(l===oo){f++;let A={type:"brace",open:!0,close:!1,dollar:D.value&&D.value.slice(-1)==="$"||i.dollar===!0,depth:f,commas:0,ranges:0,nodes:[]};i=C(A),r.push(i),C({type:"open",value:l});continue}if(l===ao){if(i.type!=="brace"){C({type:"text",value:l});continue}let F="close";i=r.pop(),i.close=!0,C({type:F,value:l}),f--,i=r[r.length-1];continue}if(l===so&&f>0){if(i.ranges>0){i.ranges=0;let F=i.nodes.shift();i.nodes=[F,{type:"text",value:uo(i)}]}C({type:"comma",value:l}),i.commas++;continue}if(l===ro&&f>0&&i.commas===0){let F=i.nodes;if(f===0||F.length===0){C({type:"text",value:l});continue}if(D.type==="dot"){if(i.range=[],D.value+=l,D.type="range",i.nodes.length!==3&&i.nodes.length!==5){i.invalid=!0,i.ranges=0,D.type="text";continue}i.ranges++,i.args=[];continue}if(D.type==="range"){F.pop();let A=F[F.length-1];A.value+=D.value+l,D=A,i.ranges--;continue}C({type:"dot",value:l});continue}C({type:"text",value:l})}do if(i=r.pop(),i.type!=="root"){i.nodes.forEach(B=>{B.nodes||(B.type==="open"&&(B.isOpen=!0),B.type==="close"&&(B.isClose=!0),B.nodes||(B.type="text"),B.invalid=!0)});let F=r[r.length-1],A=F.nodes.indexOf(i);F.nodes.splice(A,1,...i.nodes)}while(r.length>0);return C({type:"eos"}),s};var po=Eo;const zn=jt,Co=QD,Fo=eo,go=po,Y=(t,e={})=>{let u=[];if(Array.isArray(t))for(let n of t){let s=Y.create(n,e);Array.isArray(s)?u.push(...s):u.push(s)}else u=[].concat(Y.create(t,e));return e&&e.expand===!0&&e.nodupes===!0&&(u=[...new Set(u)]),u};Y.parse=(t,e={})=>go(t,e),Y.stringify=(t,e={})=>zn(typeof t=="string"?Y.parse(t,e):t,e),Y.compile=(t,e={})=>(typeof t=="string"&&(t=Y.parse(t,e)),Co(t,e)),Y.expand=(t,e={})=>{typeof t=="string"&&(t=Y.parse(t,e));let u=Fo(t,e);return e.noempty===!0&&(u=u.filter(Boolean)),e.nodupes===!0&&(u=[...new Set(u)]),u},Y.create=(t,e={})=>t===""||t.length<3?[t]:e.expand!==!0?Y.compile(t,e):Y.expand(t,e);var mo=Y,Yn={exports:{}},_o=["3dm","3ds","3g2","3gp","7z","a","aac","adp","ai","aif","aiff","alz","ape","apk","appimage","ar","arj","asf","au","avi","bak","baml","bh","bin","bk","bmp","btif","bz2","bzip2","cab","caf","cgm","class","cmx","cpio","cr2","cur","dat","dcm","deb","dex","djvu","dll","dmg","dng","doc","docm","docx","dot","dotm","dra","DS_Store","dsk","dts","dtshd","dvb","dwg","dxf","ecelp4800","ecelp7470","ecelp9600","egg","eol","eot","epub","exe","f4v","fbs","fh","fla","flac","flatpak","fli","flv","fpx","fst","fvt","g3","gh","gif","graffle","gz","gzip","h261","h263","h264","icns","ico","ief","img","ipa","iso","jar","jpeg","jpg","jpgv","jpm","jxr","key","ktx","lha","lib","lvp","lz","lzh","lzma","lzo","m3u","m4a","m4v","mar","mdi","mht","mid","midi","mj2","mka","mkv","mmr","mng","mobi","mov","movie","mp3","mp4","mp4a","mpeg","mpg","mpga","mxu","nef","npx","numbers","nupkg","o","odp","ods","odt","oga","ogg","ogv","otf","ott","pages","pbm","pcx","pdb","pdf","pea","pgm","pic","png","pnm","pot","potm","potx","ppa","ppam","ppm","pps","ppsm","ppsx","ppt","pptm","pptx","psd","pya","pyc","pyo","pyv","qt","rar","ras","raw","resources","rgb","rip","rlc","rmf","rmvb","rpm","rtf","rz","s3m","s7z","scpt","sgi","shar","snap","sil","sketch","slk","smv","snk","so","stl","suo","sub","swf","tar","tbz","tbz2","tga","tgz","thmx","tif","tiff","tlz","ttc","ttf","txz","udf","uvh","uvi","uvm","uvp","uvs","uvu","viv","vob","war","wav","wax","wbmp","wdp","weba","webm","webp","whl","wim","wm","wma","wmv","wmx","woff","woff2","wrm","wvx","xbm","xif","xla","xlam","xls","xlsb","xlsm","xlsx","xlt","xltm","xltx","xm","xmind","xpi","xpm","xwd","xz","z","zip","zipx"];(function(t){t.exports=_o})(Yn);const Ao=K,yo=Yn.exports,wo=new Set(yo);var Ro=t=>wo.has(Ao.extname(t).slice(1).toLowerCase()),Ze={};(function(t){const{sep:e}=K,{platform:u}=process,n=Fu;t.EV_ALL="all",t.EV_READY="ready",t.EV_ADD="add",t.EV_CHANGE="change",t.EV_ADD_DIR="addDir",t.EV_UNLINK="unlink",t.EV_UNLINK_DIR="unlinkDir",t.EV_RAW="raw",t.EV_ERROR="error",t.STR_DATA="data",t.STR_END="end",t.STR_CLOSE="close",t.FSEVENT_CREATED="created",t.FSEVENT_MODIFIED="modified",t.FSEVENT_DELETED="deleted",t.FSEVENT_MOVED="moved",t.FSEVENT_CLONED="cloned",t.FSEVENT_UNKNOWN="unknown",t.FSEVENT_TYPE_FILE="file",t.FSEVENT_TYPE_DIRECTORY="directory",t.FSEVENT_TYPE_SYMLINK="symlink",t.KEY_LISTENERS="listeners",t.KEY_ERR="errHandlers",t.KEY_RAW="rawEmitters",t.HANDLER_KEYS=[t.KEY_LISTENERS,t.KEY_ERR,t.KEY_RAW],t.DOT_SLASH=`.${e}`,t.BACK_SLASH_RE=/\\/g,t.DOUBLE_SLASH_RE=/\/\//,t.SLASH_OR_BACK_SLASH_RE=/[/\\]/,t.DOT_RE=/\..*\.(sw[px])$|~$|\.subl.*\.tmp/,t.REPLACER_RE=/^\.[/\\]/,t.SLASH="/",t.SLASH_SLASH="//",t.BRACE_START="{",t.BANG="!",t.ONE_DOT=".",t.TWO_DOTS="..",t.STAR="*",t.GLOBSTAR="**",t.ROOT_GLOBSTAR="/**/*",t.SLASH_GLOBSTAR="/**",t.DIR_SUFFIX="Dir",t.ANYMATCH_OPTS={dot:!0},t.STRING_TYPE="string",t.FUNCTION_TYPE="function",t.EMPTY_STR="",t.EMPTY_FN=()=>{},t.IDENTITY_FN=s=>s,t.isWindows=u==="win32",t.isMacos=u==="darwin",t.isLinux=u==="linux",t.isIBMi=n.type()==="OS400"})(Ze);const ue=ie,L=K,{promisify:ve}=me,bo=Ro,{isWindows:vo,isLinux:Bo,EMPTY_FN:So,EMPTY_STR:$o,KEY_LISTENERS:Ce,KEY_ERR:Yt,KEY_RAW:Be,HANDLER_KEYS:To,EV_CHANGE:Je,EV_ADD:et,EV_ADD_DIR:xo,EV_ERROR:qn,STR_DATA:Oo,STR_END:No,BRACE_START:Ho,STAR:Po}=Ze,Lo="watch",Io=ve(ue.open),Xn=ve(ue.stat),ko=ve(ue.lstat),Mo=ve(ue.close),qt=ve(ue.realpath),Wo={lstat:ko,stat:Xn},Xt=(t,e)=>{t instanceof Set?t.forEach(e):e(t)},Se=(t,e,u)=>{let n=t[e];n instanceof Set||(t[e]=n=new Set([n])),n.add(u)},Go=t=>e=>{const u=t[e];u instanceof Set?u.clear():delete t[e]},$e=(t,e,u)=>{const n=t[e];n instanceof Set?n.delete(u):n===u&&delete t[e]},Qn=t=>t instanceof Set?t.size===0:!t,tt=new Map;function Zn(t,e,u,n,s){const r=(i,D)=>{u(t),s(i,D,{watchedPath:t}),D&&t!==D&&ut(L.resolve(t,D),Ce,L.join(t,D))};try{return ue.watch(t,e,r)}catch(i){n(i)}}const ut=(t,e,u,n,s)=>{const r=tt.get(t);!r||Xt(r[e],i=>{i(u,n,s)})},jo=(t,e,u,n)=>{const{listener:s,errHandler:r,rawEmitter:i}=n;let D=tt.get(e),o;if(!u.persistent)return o=Zn(t,u,s,r,i),o.close.bind(o);if(D)Se(D,Ce,s),Se(D,Yt,r),Se(D,Be,i);else{if(o=Zn(t,u,ut.bind(null,e,Ce),r,ut.bind(null,e,Be)),!o)return;o.on(qn,async a=>{const c=ut.bind(null,e,Yt);if(D.watcherUnusable=!0,vo&&a.code==="EPERM")try{const f=await Io(t,"r");await Mo(f),c(a)}catch{}else c(a)}),D={listeners:s,errHandlers:r,rawEmitters:i,watcher:o},tt.set(e,D)}return()=>{$e(D,Ce,s),$e(D,Yt,r),$e(D,Be,i),Qn(D.listeners)&&(D.watcher.close(),tt.delete(e),To.forEach(Go(D)),D.watcher=void 0,Object.freeze(D))}},Qt=new Map,Uo=(t,e,u,n)=>{const{listener:s,rawEmitter:r}=n;let i=Qt.get(e);const D=i&&i.options;return D&&(D.persistent<u.persistent||D.interval>u.interval)&&(i.listeners,i.rawEmitters,ue.unwatchFile(e),i=void 0),i?(Se(i,Ce,s),Se(i,Be,r)):(i={listeners:s,rawEmitters:r,options:u,watcher:ue.watchFile(e,u,(o,a)=>{Xt(i.rawEmitters,f=>{f(Je,e,{curr:o,prev:a})});const c=o.mtimeMs;(o.size!==a.size||c>a.mtimeMs||c===0)&&Xt(i.listeners,f=>f(t,o))})},Qt.set(e,i)),()=>{$e(i,Ce,s),$e(i,Be,r),Qn(i.listeners)&&(Qt.delete(e),ue.unwatchFile(e),i.options=i.watcher=void 0,Object.freeze(i))}};class Ko{constructor(e){this.fsw=e,this._boundHandleError=u=>e._handleError(u)}_watchWithNodeFs(e,u){const n=this.fsw.options,s=L.dirname(e),r=L.basename(e);this.fsw._getWatchedDir(s).add(r);const D=L.resolve(e),o={persistent:n.persistent};u||(u=So);let a;return n.usePolling?(o.interval=n.enableBinaryInterval&&bo(r)?n.binaryInterval:n.interval,a=Uo(e,D,o,{listener:u,rawEmitter:this.fsw._emitRaw})):a=jo(e,D,o,{listener:u,errHandler:this._boundHandleError,rawEmitter:this.fsw._emitRaw}),a}_handleFile(e,u,n){if(this.fsw.closed)return;const s=L.dirname(e),r=L.basename(e),i=this.fsw._getWatchedDir(s);let D=u;if(i.has(r))return;const o=async(c,f)=>{if(!!this.fsw._throttle(Lo,e,5)){if(!f||f.mtimeMs===0)try{const l=await Xn(e);if(this.fsw.closed)return;const p=l.atimeMs,C=l.mtimeMs;(!p||p<=C||C!==D.mtimeMs)&&this.fsw._emit(Je,e,l),Bo&&D.ino!==l.ino?(this.fsw._closeFile(c),D=l,this.fsw._addPathCloser(c,this._watchWithNodeFs(e,o))):D=l}catch{this.fsw._remove(s,r)}else if(i.has(r)){const l=f.atimeMs,p=f.mtimeMs;(!l||l<=p||p!==D.mtimeMs)&&this.fsw._emit(Je,e,f),D=f}}},a=this._watchWithNodeFs(e,o);if(!(n&&this.fsw.options.ignoreInitial)&&this.fsw._isntIgnored(e)){if(!this.fsw._throttle(et,e,0))return;this.fsw._emit(et,e,u)}return a}async _handleSymlink(e,u,n,s){if(this.fsw.closed)return;const r=e.fullPath,i=this.fsw._getWatchedDir(u);if(!this.fsw.options.followSymlinks){this.fsw._incrReadyCount();let D;try{D=await qt(n)}catch{return this.fsw._emitReady(),!0}return this.fsw.closed?void 0:(i.has(s)?this.fsw._symlinkPaths.get(r)!==D&&(this.fsw._symlinkPaths.set(r,D),this.fsw._emit(Je,n,e.stats)):(i.add(s),this.fsw._symlinkPaths.set(r,D),this.fsw._emit(et,n,e.stats)),this.fsw._emitReady(),!0)}if(this.fsw._symlinkPaths.has(r))return!0;this.fsw._symlinkPaths.set(r,!0)}_handleRead(e,u,n,s,r,i,D){if(e=L.join(e,$o),!n.hasGlob&&(D=this.fsw._throttle("readdir",e,1e3),!D))return;const o=this.fsw._getWatchedDir(n.path),a=new Set;let c=this.fsw._readdirp(e,{fileFilter:f=>n.filterPath(f),directoryFilter:f=>n.filterDir(f),depth:0}).on(Oo,async f=>{if(this.fsw.closed){c=void 0;return}const l=f.path;let p=L.join(e,l);if(a.add(l),!(f.stats.isSymbolicLink()&&await this._handleSymlink(f,e,p,l))){if(this.fsw.closed){c=void 0;return}(l===s||!s&&!o.has(l))&&(this.fsw._incrReadyCount(),p=L.join(r,L.relative(r,p)),this._addToNodeFs(p,u,n,i+1))}}).on(qn,this._boundHandleError);return new Promise(f=>c.once(No,()=>{if(this.fsw.closed){c=void 0;return}const l=D?D.clear():!1;f(),o.getChildren().filter(p=>p!==e&&!a.has(p)&&(!n.hasGlob||n.filterPath({fullPath:L.resolve(e,p)}))).forEach(p=>{this.fsw._remove(e,p)}),c=void 0,l&&this._handleRead(e,!1,n,s,r,i,D)}))}async _handleDir(e,u,n,s,r,i,D){const o=this.fsw._getWatchedDir(L.dirname(e)),a=o.has(L.basename(e));!(n&&this.fsw.options.ignoreInitial)&&!r&&!a&&(!i.hasGlob||i.globFilter(e))&&this.fsw._emit(xo,e,u),o.add(L.basename(e)),this.fsw._getWatchedDir(e);let c,f;const l=this.fsw.options.depth;if((l==null||s<=l)&&!this.fsw._symlinkPaths.has(D)){if(!r&&(await this._handleRead(e,n,i,r,e,s,c),this.fsw.closed))return;f=this._watchWithNodeFs(e,(p,C)=>{C&&C.mtimeMs===0||this._handleRead(p,!1,i,r,e,s,c)})}return f}async _addToNodeFs(e,u,n,s,r){const i=this.fsw._emitReady;if(this.fsw._isIgnored(e)||this.fsw.closed)return i(),!1;const D=this.fsw._getWatchHelpers(e,s);!D.hasGlob&&n&&(D.hasGlob=n.hasGlob,D.globFilter=n.globFilter,D.filterPath=o=>n.filterPath(o),D.filterDir=o=>n.filterDir(o));try{const o=await Wo[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))return i(),!1;const a=this.fsw.options.followSymlinks&&!e.includes(Po)&&!e.includes(Ho);let c;if(o.isDirectory()){const f=L.resolve(e),l=a?await qt(e):e;if(this.fsw.closed||(c=await this._handleDir(D.watchPath,o,u,s,r,D,l),this.fsw.closed))return;f!==l&&l!==void 0&&this.fsw._symlinkPaths.set(f,l)}else if(o.isSymbolicLink()){const f=a?await qt(e):e;if(this.fsw.closed)return;const l=L.dirname(D.watchPath);if(this.fsw._getWatchedDir(l).add(D.watchPath),this.fsw._emit(et,D.watchPath,o),c=await this._handleDir(l,o,u,s,e,D,f),this.fsw.closed)return;f!==void 0&&this.fsw._symlinkPaths.set(L.resolve(e),f)}else c=this._handleFile(D.watchPath,o,u);return i(),this.fsw._addPathCloser(e,c),!1}catch(o){if(this.fsw._handleError(o))return i(),e}}}var Vo=Ko,Zt={exports:{}};const Jt=ie,I=K,{promisify:eu}=me;let Fe;try{Fe=Dt("fsevents")}catch(t){process.env.CHOKIDAR_PRINT_FSEVENTS_REQUIRE_ERROR&&console.error(t)}if(Fe){const t=process.version.match(/v(\d+)\.(\d+)/);if(t&&t[1]&&t[2]){const e=Number.parseInt(t[1],10),u=Number.parseInt(t[2],10);e===8&&u<16&&(Fe=void 0)}}const{EV_ADD:tu,EV_CHANGE:zo,EV_ADD_DIR:Jn,EV_UNLINK:nt,EV_ERROR:Yo,STR_DATA:qo,STR_END:Xo,FSEVENT_CREATED:Qo,FSEVENT_MODIFIED:Zo,FSEVENT_DELETED:Jo,FSEVENT_MOVED:ea,FSEVENT_UNKNOWN:ta,FSEVENT_TYPE_FILE:ua,FSEVENT_TYPE_DIRECTORY:Te,FSEVENT_TYPE_SYMLINK:es,ROOT_GLOBSTAR:ts,DIR_SUFFIX:na,DOT_SLASH:us,FUNCTION_TYPE:uu,EMPTY_FN:sa,IDENTITY_FN:ra}=Ze,ia=t=>isNaN(t)?{}:{depth:t},nu=eu(Jt.stat),Da=eu(Jt.lstat),ns=eu(Jt.realpath),oa={stat:nu,lstat:Da},le=new Map,aa=10,la=new Set([69888,70400,71424,72704,73472,131328,131840,262912]),ca=(t,e)=>({stop:Fe.watch(t,e)});function fa(t,e,u,n){let s=I.extname(e)?I.dirname(e):e;const r=I.dirname(s);let i=le.get(s);ha(r)&&(s=r);const D=I.resolve(t),o=D!==e,a=(f,l,p)=>{o&&(f=f.replace(e,D)),(f===D||!f.indexOf(D+I.sep))&&u(f,l,p)};let c=!1;for(const f of le.keys())if(e.indexOf(I.resolve(f)+I.sep)===0){s=f,i=le.get(s),c=!0;break}return i||c?i.listeners.add(a):(i={listeners:new Set([a]),rawEmitter:n,watcher:ca(s,(f,l)=>{if(!i.listeners.size)return;const p=Fe.getInfo(f,l);i.listeners.forEach(C=>{C(f,l,p)}),i.rawEmitter(p.event,f,p)})},le.set(s,i)),()=>{const f=i.listeners;if(f.delete(a),!f.size&&(le.delete(s),i.watcher))return i.watcher.stop().then(()=>{i.rawEmitter=i.watcher=void 0,Object.freeze(i)})}}const ha=t=>{let e=0;for(const u of le.keys())if(u.indexOf(t)===0&&(e++,e>=aa))return!0;return!1},da=()=>Fe&&le.size<128,su=(t,e)=>{let u=0;for(;!t.indexOf(e)&&(t=I.dirname(t))!==e;)u++;return u},ss=(t,e)=>t.type===Te&&e.isDirectory()||t.type===es&&e.isSymbolicLink()||t.type===ua&&e.isFile();class Ea{constructor(e){this.fsw=e}checkIgnored(e,u){const n=this.fsw._ignoredPaths;if(this.fsw._isIgnored(e,u))return n.add(e),u&&u.isDirectory()&&n.add(e+ts),!0;n.delete(e),n.delete(e+ts)}addOrChange(e,u,n,s,r,i,D,o){const a=r.has(i)?zo:tu;this.handleEvent(a,e,u,n,s,r,i,D,o)}async checkExists(e,u,n,s,r,i,D,o){try{const a=await nu(e);if(this.fsw.closed)return;ss(D,a)?this.addOrChange(e,u,n,s,r,i,D,o):this.handleEvent(nt,e,u,n,s,r,i,D,o)}catch(a){a.code==="EACCES"?this.addOrChange(e,u,n,s,r,i,D,o):this.handleEvent(nt,e,u,n,s,r,i,D,o)}}handleEvent(e,u,n,s,r,i,D,o,a){if(!(this.fsw.closed||this.checkIgnored(u)))if(e===nt){const c=o.type===Te;(c||i.has(D))&&this.fsw._remove(r,D,c)}else{if(e===tu){if(o.type===Te&&this.fsw._getWatchedDir(u),o.type===es&&a.followSymlinks){const f=a.depth===void 0?void 0:su(n,s)+1;return this._addToFsEvents(u,!1,!0,f)}this.fsw._getWatchedDir(r).add(D)}const c=o.type===Te?e+na:e;this.fsw._emit(c,u),c===Jn&&this._addToFsEvents(u,!1,!0)}}_watchWithFsEvents(e,u,n,s){if(this.fsw.closed||this.fsw._isIgnored(e))return;const r=this.fsw.options,D=fa(e,u,async(o,a,c)=>{if(this.fsw.closed||r.depth!==void 0&&su(o,u)>r.depth)return;const f=n(I.join(e,I.relative(e,o)));if(s&&!s(f))return;const l=I.dirname(f),p=I.basename(f),C=this.fsw._getWatchedDir(c.type===Te?f:l);if(la.has(a)||c.event===ta)if(typeof r.ignored===uu){let F;try{F=await nu(f)}catch{}if(this.fsw.closed||this.checkIgnored(f,F))return;ss(c,F)?this.addOrChange(f,o,u,l,C,p,c,r):this.handleEvent(nt,f,o,u,l,C,p,c,r)}else this.checkExists(f,o,u,l,C,p,c,r);else switch(c.event){case Qo:case Zo:return this.addOrChange(f,o,u,l,C,p,c,r);case Jo:case ea:return this.checkExists(f,o,u,l,C,p,c,r)}},this.fsw._emitRaw);return this.fsw._emitReady(),D}async _handleFsEventsSymlink(e,u,n,s){if(!(this.fsw.closed||this.fsw._symlinkPaths.has(u))){this.fsw._symlinkPaths.set(u,!0),this.fsw._incrReadyCount();try{const r=await ns(e);if(this.fsw.closed)return;if(this.fsw._isIgnored(r))return this.fsw._emitReady();this.fsw._incrReadyCount(),this._addToFsEvents(r||e,i=>{let D=e;return r&&r!==us?D=i.replace(r,e):i!==us&&(D=I.join(e,i)),n(D)},!1,s)}catch(r){if(this.fsw._handleError(r))return this.fsw._emitReady()}}}emitAdd(e,u,n,s,r){const i=n(e),D=u.isDirectory(),o=this.fsw._getWatchedDir(I.dirname(i)),a=I.basename(i);D&&this.fsw._getWatchedDir(i),!o.has(a)&&(o.add(a),(!s.ignoreInitial||r===!0)&&this.fsw._emit(D?Jn:tu,i,u))}initWatch(e,u,n,s){if(this.fsw.closed)return;const r=this._watchWithFsEvents(n.watchPath,I.resolve(e||n.watchPath),s,n.globFilter);this.fsw._addPathCloser(u,r)}async _addToFsEvents(e,u,n,s){if(this.fsw.closed)return;const r=this.fsw.options,i=typeof u===uu?u:ra,D=this.fsw._getWatchHelpers(e);try{const o=await oa[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))throw null;if(o.isDirectory()){if(D.globFilter||this.emitAdd(i(e),o,i,r,n),s&&s>r.depth)return;this.fsw._readdirp(D.watchPath,{fileFilter:a=>D.filterPath(a),directoryFilter:a=>D.filterDir(a),...ia(r.depth-(s||0))}).on(qo,a=>{if(this.fsw.closed||a.stats.isDirectory()&&!D.filterPath(a))return;const c=I.join(D.watchPath,a.path),{fullPath:f}=a;if(D.followSymlinks&&a.stats.isSymbolicLink()){const l=r.depth===void 0?void 0:su(c,I.resolve(D.watchPath))+1;this._handleFsEventsSymlink(c,f,i,l)}else this.emitAdd(c,a.stats,i,r,n)}).on(Yo,sa).on(Xo,()=>{this.fsw._emitReady()})}else this.emitAdd(D.watchPath,o,i,r,n),this.fsw._emitReady()}catch(o){(!o||this.fsw._handleError(o))&&(this.fsw._emitReady(),this.fsw._emitReady())}if(r.persistent&&n!==!0)if(typeof u===uu)this.initWatch(void 0,e,D,i);else{let o;try{o=await ns(D.watchPath)}catch{}this.initWatch(o,e,D,i)}}}Zt.exports=Ea,Zt.exports.canUse=da;const{EventEmitter:pa}=Ss,ru=ie,v=K,{promisify:rs}=me,Ca=ED,iu=Mt.exports.default,Fa=TD,Du=Rn,ga=mo,ma=mn,_a=Vo,is=Zt.exports,{EV_ALL:ou,EV_READY:Aa,EV_ADD:st,EV_CHANGE:xe,EV_UNLINK:Ds,EV_ADD_DIR:ya,EV_UNLINK_DIR:wa,EV_RAW:Ra,EV_ERROR:au,STR_CLOSE:ba,STR_END:va,BACK_SLASH_RE:Ba,DOUBLE_SLASH_RE:os,SLASH_OR_BACK_SLASH_RE:Sa,DOT_RE:$a,REPLACER_RE:Ta,SLASH:lu,SLASH_SLASH:xa,BRACE_START:Oa,BANG:cu,ONE_DOT:as,TWO_DOTS:Na,GLOBSTAR:Ha,SLASH_GLOBSTAR:fu,ANYMATCH_OPTS:hu,STRING_TYPE:du,FUNCTION_TYPE:Pa,EMPTY_STR:Eu,EMPTY_FN:La,isWindows:Ia,isMacos:ka,isIBMi:Ma}=Ze,Wa=rs(ru.stat),Ga=rs(ru.readdir),pu=(t=[])=>Array.isArray(t)?t:[t],ls=(t,e=[])=>(t.forEach(u=>{Array.isArray(u)?ls(u,e):e.push(u)}),e),cs=t=>{const e=ls(pu(t));if(!e.every(u=>typeof u===du))throw new TypeError(`Non-string provided as watch path: ${e}`);return e.map(hs)},fs=t=>{let e=t.replace(Ba,lu),u=!1;for(e.startsWith(xa)&&(u=!0);e.match(os);)e=e.replace(os,lu);return u&&(e=lu+e),e},hs=t=>fs(v.normalize(fs(t))),ds=(t=Eu)=>e=>typeof e!==du?e:hs(v.isAbsolute(e)?e:v.join(t,e)),ja=(t,e)=>v.isAbsolute(t)?t:t.startsWith(cu)?cu+v.join(e,t.slice(1)):v.join(e,t),q=(t,e)=>t[e]===void 0;class Ua{constructor(e,u){this.path=e,this._removeWatcher=u,this.items=new Set}add(e){const{items:u}=this;!u||e!==as&&e!==Na&&u.add(e)}async remove(e){const{items:u}=this;if(!u||(u.delete(e),u.size>0))return;const n=this.path;try{await Ga(n)}catch{this._removeWatcher&&this._removeWatcher(v.dirname(n),v.basename(n))}}has(e){const{items:u}=this;if(!!u)return u.has(e)}getChildren(){const{items:e}=this;if(!!e)return[...e.values()]}dispose(){this.items.clear(),delete this.path,delete this._removeWatcher,delete this.items,Object.freeze(this)}}const Ka="stat",Va="lstat";class za{constructor(e,u,n,s){this.fsw=s,this.path=e=e.replace(Ta,Eu),this.watchPath=u,this.fullWatchPath=v.resolve(u),this.hasGlob=u!==e,e===Eu&&(this.hasGlob=!1),this.globSymlink=this.hasGlob&&n?void 0:!1,this.globFilter=this.hasGlob?iu(e,void 0,hu):!1,this.dirParts=this.getDirParts(e),this.dirParts.forEach(r=>{r.length>1&&r.pop()}),this.followSymlinks=n,this.statMethod=n?Ka:Va}checkGlobSymlink(e){return this.globSymlink===void 0&&(this.globSymlink=e.fullParentDir===this.fullWatchPath?!1:{realPath:e.fullParentDir,linkPath:this.fullWatchPath}),this.globSymlink?e.fullPath.replace(this.globSymlink.realPath,this.globSymlink.linkPath):e.fullPath}entryPath(e){return v.join(this.watchPath,v.relative(this.watchPath,this.checkGlobSymlink(e)))}filterPath(e){const{stats:u}=e;if(u&&u.isSymbolicLink())return this.filterDir(e);const n=this.entryPath(e);return(this.hasGlob&&typeof this.globFilter===Pa?this.globFilter(n):!0)&&this.fsw._isntIgnored(n,u)&&this.fsw._hasReadPermissions(u)}getDirParts(e){if(!this.hasGlob)return[];const u=[];return(e.includes(Oa)?ga.expand(e):[e]).forEach(s=>{u.push(v.relative(this.watchPath,s).split(Sa))}),u}filterDir(e){if(this.hasGlob){const u=this.getDirParts(this.checkGlobSymlink(e));let n=!1;this.unmatchedGlob=!this.dirParts.some(s=>s.every((r,i)=>(r===Ha&&(n=!0),n||!u[0][i]||iu(r,u[0][i],hu))))}return!this.unmatchedGlob&&this.fsw._isntIgnored(this.entryPath(e),e.stats)}}class Ya extends pa{constructor(e){super();const u={};e&&Object.assign(u,e),this._watched=new Map,this._closers=new Map,this._ignoredPaths=new Set,this._throttled=new Map,this._symlinkPaths=new Map,this._streams=new Set,this.closed=!1,q(u,"persistent")&&(u.persistent=!0),q(u,"ignoreInitial")&&(u.ignoreInitial=!1),q(u,"ignorePermissionErrors")&&(u.ignorePermissionErrors=!1),q(u,"interval")&&(u.interval=100),q(u,"binaryInterval")&&(u.binaryInterval=300),q(u,"disableGlobbing")&&(u.disableGlobbing=!1),u.enableBinaryInterval=u.binaryInterval!==u.interval,q(u,"useFsEvents")&&(u.useFsEvents=!u.usePolling),is.canUse()||(u.useFsEvents=!1),q(u,"usePolling")&&!u.useFsEvents&&(u.usePolling=ka),Ma&&(u.usePolling=!0);const s=process.env.CHOKIDAR_USEPOLLING;if(s!==void 0){const o=s.toLowerCase();o==="false"||o==="0"?u.usePolling=!1:o==="true"||o==="1"?u.usePolling=!0:u.usePolling=!!o}const r=process.env.CHOKIDAR_INTERVAL;r&&(u.interval=Number.parseInt(r,10)),q(u,"atomic")&&(u.atomic=!u.usePolling&&!u.useFsEvents),u.atomic&&(this._pendingUnlinks=new Map),q(u,"followSymlinks")&&(u.followSymlinks=!0),q(u,"awaitWriteFinish")&&(u.awaitWriteFinish=!1),u.awaitWriteFinish===!0&&(u.awaitWriteFinish={});const i=u.awaitWriteFinish;i&&(i.stabilityThreshold||(i.stabilityThreshold=2e3),i.pollInterval||(i.pollInterval=100),this._pendingWrites=new Map),u.ignored&&(u.ignored=pu(u.ignored));let D=0;this._emitReady=()=>{D++,D>=this._readyCount&&(this._emitReady=La,this._readyEmitted=!0,process.nextTick(()=>this.emit(Aa)))},this._emitRaw=(...o)=>this.emit(Ra,...o),this._readyEmitted=!1,this.options=u,u.useFsEvents?this._fsEventsHandler=new is(this):this._nodeFsHandler=new _a(this),Object.freeze(u)}add(e,u,n){const{cwd:s,disableGlobbing:r}=this.options;this.closed=!1;let i=cs(e);return s&&(i=i.map(D=>{const o=ja(D,s);return r||!Du(D)?o:ma(o)})),i=i.filter(D=>D.startsWith(cu)?(this._ignoredPaths.add(D.slice(1)),!1):(this._ignoredPaths.delete(D),this._ignoredPaths.delete(D+fu),this._userIgnored=void 0,!0)),this.options.useFsEvents&&this._fsEventsHandler?(this._readyCount||(this._readyCount=i.length),this.options.persistent&&(this._readyCount*=2),i.forEach(D=>this._fsEventsHandler._addToFsEvents(D))):(this._readyCount||(this._readyCount=0),this._readyCount+=i.length,Promise.all(i.map(async D=>{const o=await this._nodeFsHandler._addToNodeFs(D,!n,0,0,u);return o&&this._emitReady(),o})).then(D=>{this.closed||D.filter(o=>o).forEach(o=>{this.add(v.dirname(o),v.basename(u||o))})})),this}unwatch(e){if(this.closed)return this;const u=cs(e),{cwd:n}=this.options;return u.forEach(s=>{!v.isAbsolute(s)&&!this._closers.has(s)&&(n&&(s=v.join(n,s)),s=v.resolve(s)),this._closePath(s),this._ignoredPaths.add(s),this._watched.has(s)&&this._ignoredPaths.add(s+fu),this._userIgnored=void 0}),this}close(){if(this.closed)return this._closePromise;this.closed=!0,this.removeAllListeners();const e=[];return this._closers.forEach(u=>u.forEach(n=>{const s=n();s instanceof Promise&&e.push(s)})),this._streams.forEach(u=>u.destroy()),this._userIgnored=void 0,this._readyCount=0,this._readyEmitted=!1,this._watched.forEach(u=>u.dispose()),["closers","watched","streams","symlinkPaths","throttled"].forEach(u=>{this[`_${u}`].clear()}),this._closePromise=e.length?Promise.all(e).then(()=>{}):Promise.resolve(),this._closePromise}getWatched(){const e={};return this._watched.forEach((u,n)=>{const s=this.options.cwd?v.relative(this.options.cwd,n):n;e[s||as]=u.getChildren().sort()}),e}emitWithAll(e,u){this.emit(...u),e!==au&&this.emit(ou,...u)}async _emit(e,u,n,s,r){if(this.closed)return;const i=this.options;Ia&&(u=v.normalize(u)),i.cwd&&(u=v.relative(i.cwd,u));const D=[e,u];r!==void 0?D.push(n,s,r):s!==void 0?D.push(n,s):n!==void 0&&D.push(n);const o=i.awaitWriteFinish;let a;if(o&&(a=this._pendingWrites.get(u)))return a.lastChange=new Date,this;if(i.atomic){if(e===Ds)return this._pendingUnlinks.set(u,D),setTimeout(()=>{this._pendingUnlinks.forEach((c,f)=>{this.emit(...c),this.emit(ou,...c),this._pendingUnlinks.delete(f)})},typeof i.atomic=="number"?i.atomic:100),this;e===st&&this._pendingUnlinks.has(u)&&(e=D[0]=xe,this._pendingUnlinks.delete(u))}if(o&&(e===st||e===xe)&&this._readyEmitted){const c=(f,l)=>{f?(e=D[0]=au,D[1]=f,this.emitWithAll(e,D)):l&&(D.length>2?D[2]=l:D.push(l),this.emitWithAll(e,D))};return this._awaitWriteFinish(u,o.stabilityThreshold,e,c),this}if(e===xe&&!this._throttle(xe,u,50))return this;if(i.alwaysStat&&n===void 0&&(e===st||e===ya||e===xe)){const c=i.cwd?v.join(i.cwd,u):u;let f;try{f=await Wa(c)}catch{}if(!f||this.closed)return;D.push(f)}return this.emitWithAll(e,D),this}_handleError(e){const u=e&&e.code;return e&&u!=="ENOENT"&&u!=="ENOTDIR"&&(!this.options.ignorePermissionErrors||u!=="EPERM"&&u!=="EACCES")&&this.emit(au,e),e||this.closed}_throttle(e,u,n){this._throttled.has(e)||this._throttled.set(e,new Map);const s=this._throttled.get(e),r=s.get(u);if(r)return r.count++,!1;let i;const D=()=>{const a=s.get(u),c=a?a.count:0;return s.delete(u),clearTimeout(i),a&&clearTimeout(a.timeoutObject),c};i=setTimeout(D,n);const o={timeoutObject:i,clear:D,count:0};return s.set(u,o),o}_incrReadyCount(){return this._readyCount++}_awaitWriteFinish(e,u,n,s){let r,i=e;this.options.cwd&&!v.isAbsolute(e)&&(i=v.join(this.options.cwd,e));const D=new Date,o=a=>{ru.stat(i,(c,f)=>{if(c||!this._pendingWrites.has(e)){c&&c.code!=="ENOENT"&&s(c);return}const l=Number(new Date);a&&f.size!==a.size&&(this._pendingWrites.get(e).lastChange=l);const p=this._pendingWrites.get(e);l-p.lastChange>=u?(this._pendingWrites.delete(e),s(void 0,f)):r=setTimeout(o,this.options.awaitWriteFinish.pollInterval,f)})};this._pendingWrites.has(e)||(this._pendingWrites.set(e,{lastChange:D,cancelWait:()=>(this._pendingWrites.delete(e),clearTimeout(r),n)}),r=setTimeout(o,this.options.awaitWriteFinish.pollInterval))}_getGlobIgnored(){return[...this._ignoredPaths.values()]}_isIgnored(e,u){if(this.options.atomic&&$a.test(e))return!0;if(!this._userIgnored){const{cwd:n}=this.options,s=this.options.ignored,r=s&&s.map(ds(n)),i=pu(r).filter(o=>typeof o===du&&!Du(o)).map(o=>o+fu),D=this._getGlobIgnored().map(ds(n)).concat(r,i);this._userIgnored=iu(D,void 0,hu)}return this._userIgnored([e,u])}_isntIgnored(e,u){return!this._isIgnored(e,u)}_getWatchHelpers(e,u){const n=u||this.options.disableGlobbing||!Du(e)?e:Fa(e),s=this.options.followSymlinks;return new za(e,n,s,this)}_getWatchedDir(e){this._boundRemove||(this._boundRemove=this._remove.bind(this));const u=v.resolve(e);return this._watched.has(u)||this._watched.set(u,new Ua(u,this._boundRemove)),this._watched.get(u)}_hasReadPermissions(e){if(this.options.ignorePermissionErrors)return!0;const n=(e&&Number.parseInt(e.mode,10))&511,s=Number.parseInt(n.toString(8)[0],10);return Boolean(4&s)}_remove(e,u,n){const s=v.join(e,u),r=v.resolve(s);if(n=n!=null?n:this._watched.has(s)||this._watched.has(r),!this._throttle("remove",s,100))return;!n&&!this.options.useFsEvents&&this._watched.size===1&&this.add(e,u,!0),this._getWatchedDir(s).getChildren().forEach(l=>this._remove(s,l));const o=this._getWatchedDir(e),a=o.has(u);o.remove(u),this._symlinkPaths.has(r)&&this._symlinkPaths.delete(r);let c=s;if(this.options.cwd&&(c=v.relative(this.options.cwd,s)),this.options.awaitWriteFinish&&this._pendingWrites.has(c)&&this._pendingWrites.get(c).cancelWait()===st)return;this._watched.delete(s),this._watched.delete(r);const f=n?wa:Ds;a&&!this._isIgnored(s)&&this._emit(f,s),this.options.useFsEvents||this._closePath(s)}_closePath(e){this._closeFile(e);const u=v.dirname(e);this._getWatchedDir(u).remove(v.basename(e))}_closeFile(e){const u=this._closers.get(e);!u||(u.forEach(n=>n()),this._closers.delete(e))}_addPathCloser(e,u){if(!u)return;let n=this._closers.get(e);n||(n=[],this._closers.set(e,n)),n.push(u)}_readdirp(e,u){if(this.closed)return;const n={type:ou,alwaysStat:!0,lstat:!0,...u};let s=Ca(e,n);return this._streams.add(s),s.once(ba,()=>{s=void 0}),s.once(va,()=>{s&&(this._streams.delete(s),s=void 0)}),s}}const qa=(t,e)=>{const u=new Ya(e);return u.add(t),u};var Xa=qa;const Cu=(t=!0)=>{let e=!1;return u=>{if(e||u==="unknown-flag")return!0;if(u==="argument")return e=!0,t}};function Es(t,e=process.argv.slice(2)){return mu(t,e,{ignore:Cu()}),e}let ce=!0;const ge=typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{};let ps=0;if(ge.process&&ge.process.env&&ge.process.stdout){const{FORCE_COLOR:t,NODE_DISABLE_COLORS:e,TERM:u}=ge.process.env;e||t==="0"?ce=!1:t==="1"?ce=!0:u==="dumb"?ce=!1:"CI"in ge.process.env&&["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(n=>n in ge.process.env)?ce=!0:ce=process.stdout.isTTY,ce&&(ps=u&&u.endsWith("-256color")?2:1)}let Cs={enabled:ce,supportLevel:ps};function Fs(t,e,u=1){const n=`\x1B[${t}m`,s=`\x1B[${e}m`,r=new RegExp(`\\x1b\\[${e}m`,"g");return i=>Cs.enabled&&Cs.supportLevel>=u?n+(""+i).replace(r,n)+s:""+i}const Qa=Fs(90,39),Za=Fs(96,39),Ja=()=>new Date().toLocaleTimeString(),el=(...t)=>console.log(Qa(Ja()),Za("[tsx]"),...t),tl="\x1Bc";function ul(t,e){let u;return()=>{u&&clearTimeout(u),u=setTimeout(()=>t(),e)}}function nl(t){return t&&typeof t=="object"&&t.type==="dependency"}const gs={noCache:{type:Boolean,description:"Disable caching",default:!1},tsconfig:{type:String,description:"Custom tsconfig.json path"},clearScreen:{type:Boolean,description:"Clearing the screen on rerun",default:!0},ignore:{type:[String],description:"Paths & globs to exclude from being watched"}},sl=qr({name:"watch",parameters:["<script path>"],flags:gs,help:{description:"Run the script and watch for changes"},ignoreArgv:Cu(!1)},t=>{const e=Es(gs,process.argv.slice(3)),u={noCache:t.flags.noCache,tsconfigPath:t.flags.tsconfig,clearScreen:t.flags.clearScreen,ignore:t.flags.ignore,ipc:!0};let n;const s=ul(()=>{n&&!n.killed&&n.exitCode===null&&n.kill(),n&&el("rerunning"),u.clearScreen&&process.stdout.write(tl),n=tn(e,u),n.on("message",o=>{if(nl(o)){const a=o.path.startsWith("file:")?bs(o.path):o.path;K.isAbsolute(a)&&D.add(a)}})},100);s();function r(o){process.exit(128+Bs.signals[o])}function i(o){n&&n.exitCode===null?(n.on("close",()=>r(o)),n.kill(o)):r(o)}process.once("SIGINT",i),process.once("SIGTERM",i);const D=Xa(t._,{cwd:process.cwd(),ignoreInitial:!0,ignored:["**/.*/**","**/.*","**/{node_modules,bower_components,vendor}/**",...u.ignore],ignorePermissionErrors:!0}).on("all",s);process.stdin.on("data",s)}),ms={noCache:{type:Boolean,description:"Disable caching"},tsconfig:{type:String,description:"Custom tsconfig.json path"}};Yr({name:"tsx",parameters:["[script path]"],commands:[sl],flags:{...ms,version:{type:Boolean,alias:"v",description:"Show version"},help:{type:Boolean,alias:"h",description:"Show help"}},help:!1,ignoreArgv:Cu()},t=>{t.flags.version?process.stdout.write(`tsx v${ws}
node `):t.flags.help&&(t.showHelp({description:"Node.js runtime enhanced with esbuild for loading TypeScript & ESM"}),console.log(`${"-".repeat(45)}
`));const e=tn(Es(ms),{noCache:Boolean(t.flags.noCache),tsconfigPath:t.flags.tsconfig}),u=async n=>{await Promise.race([new Promise(r=>{function i(D){D&&D.type==="kill"&&(r(D.signal),e.off("message",i))}e.on("message",i)}),new Promise(r=>{setTimeout(r,10)})])||e.kill(n)};process.on("SIGINT",u),process.on("SIGTERM",u),e.on("close",n=>process.exit(n))});
