{"name": "thread-stream", "version": "2.4.1", "description": "A streaming way to send data to a Node.js Worker Thread", "main": "index.js", "types": "index.d.ts", "dependencies": {"real-require": "^0.2.0"}, "devDependencies": {"@types/node": "^20.1.0", "@types/tap": "^15.0.0", "desm": "^1.3.0", "fastbench": "^1.0.1", "husky": "^8.0.1", "pino-elasticsearch": "^6.0.0", "sonic-boom": "^3.0.0", "standard": "^17.0.0", "tap": "^16.2.0", "ts-node": "^10.8.0", "typescript": "^4.7.2", "why-is-node-running": "^2.2.2"}, "scripts": {"test": "standard && npm run transpile && tap test/*.test.*js && tap --ts test/*.test.*ts", "test:ci": "standard && npm run transpile && npm run test:ci:js && npm run test:ci:ts", "test:ci:js": "tap --no-check-coverage --coverage-report=lcovonly \"test/**/*.test.*js\"", "test:ci:ts": "tap --ts --no-check-coverage --coverage-report=lcovonly \"test/**/*.test.*ts\"", "test:yarn": "npm run transpile && tap \"test/**/*.test.js\" --no-check-coverage", "transpile": "sh ./test/ts/transpile.sh", "prepare": "husky install"}, "standard": {"ignore": ["test/ts/**/*"]}, "repository": {"type": "git", "url": "git+https://github.com/mcollina/thread-stream.git"}, "keywords": ["worker", "thread", "threads", "stream"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/mcollina/thread-stream/issues"}, "homepage": "https://github.com/mcollina/thread-stream#readme"}