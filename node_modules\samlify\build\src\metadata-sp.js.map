{"version": 3, "file": "metadata-sp.js", "sourceRoot": "", "sources": ["../../src/metadata-sp.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;EAIE;AACF,wDAAyD;AAEzD,6BAA0D;AAC1D,sDAAgC;AAChC,qCAAoE;AACpE,4CAAsB;AAetB;;GAEG;AACH,mBAAwB,IAA2B;IACjD,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC;AAFD,4BAEC;AAED;;EAEE;AACF;;IAAgC,8BAAQ;IAEtC;;;MAGE;IACF,oBAAY,IAA2B;QAErC,IAAM,MAAM,GAAG,IAAA,kBAAQ,EAAC,IAAI,CAAC,IAAI,IAAI,YAAY,MAAM,CAAC;QAExD,uEAAuE;QACvE,IAAI,CAAC,MAAM,EAAE;YAEL,IAAA,KAYF,IAAyB,EAX3B,qBAA6B,EAA7B,aAAa,mBAAG,mBAAK,CAAC,OAAO,KAAA,EAC7B,QAAQ,cAAA,EACR,WAAW,iBAAA,EACX,WAAW,iBAAA,EACX,2BAA2B,EAA3B,mBAAmB,mBAAG,KAAK,KAAA,EAC3B,4BAA4B,EAA5B,oBAAoB,mBAAG,KAAK,KAAA,EAC5B,yBAAyB,EAAzB,iBAAiB,mBAAG,KAAK,KAAA,EACzB,eAAe,qBAAA,EACf,oBAAiB,EAAjB,YAAY,mBAAG,EAAE,KAAA,EACjB,2BAAwB,EAAxB,mBAAmB,mBAAG,EAAE,KAAA,EACxB,gCAA6B,EAA7B,wBAAwB,mBAAG,EAAE,KACF,CAAC;YAE9B,IAAM,aAAW,GAAgB;gBAC/B,aAAa,EAAE,EAAE;gBACjB,YAAY,EAAE,EAAE;gBAChB,mBAAmB,EAAE,EAAE;gBACvB,wBAAwB,EAAE,EAAE;gBAC5B,yBAAyB,EAAE,EAAE;aAC9B,CAAC;YAEF,IAAM,iBAAe,GAAU,CAAC;oBAC9B,KAAK,EAAE;wBACL,mBAAmB,EAAE,MAAM,CAAC,mBAAmB,CAAC;wBAChD,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,CAAC;wBAClD,0BAA0B,EAAE,eAAS,CAAC,KAAK,CAAC,QAAQ;qBACrD;iBACF,CAAC,CAAC;YAEH,IAAI,iBAAiB,IAAI,eAAe,KAAK,SAAS,EAAE;gBACtD,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;aACtE;;gBAED,KAAkB,IAAA,KAAA,SAAA,IAAA,sBAAY,EAAC,WAAW,CAAC,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,IAAI,WAAA;oBACZ,aAAW,CAAC,aAAc,CAAC,IAAI,CAAC,iBAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;iBAC1F;;;;;;;;;;gBAED,KAAkB,IAAA,KAAA,SAAA,IAAA,sBAAY,EAAC,WAAW,CAAC,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,IAAI,WAAA;oBACZ,aAAW,CAAC,aAAc,CAAC,IAAI,CAAC,iBAAO,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;iBAC7F;;;;;;;;;YAED,IAAI,IAAA,yBAAe,EAAC,YAAY,CAAC,EAAE;gBACjC,YAAY,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,aAAW,CAAC,YAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAC;aAC9D;iBAAM;gBACL,gBAAgB;gBAChB,aAAW,CAAC,YAAa,CAAC,IAAI,CAAC,eAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aAC/D;YAED,IAAI,IAAA,yBAAe,EAAC,mBAAmB,CAAC,EAAE;gBACxC,mBAAmB,CAAC,OAAO,CAAC,UAAA,CAAC;oBAC3B,IAAM,IAAI,GAAQ;wBAChB,OAAO,EAAE,CAAC,CAAC,OAAO;wBAClB,QAAQ,EAAE,CAAC,CAAC,QAAQ;qBACrB,CAAC;oBACF,IAAI,CAAC,CAAC,SAAS,EAAE;wBACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;qBACvB;oBACD,aAAW,CAAC,mBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBAC3D,CAAC,CAAC,CAAC;aACJ;YAED,IAAI,IAAA,yBAAe,EAAC,wBAAwB,CAAC,EAAE;gBAC7C,IAAI,YAAU,GAAG,CAAC,CAAC;gBACnB,wBAAwB,CAAC,OAAO,CAAC,UAAA,CAAC;oBAChC,IAAM,IAAI,GAAQ;wBAChB,KAAK,EAAE,MAAM,CAAC,YAAU,EAAE,CAAC;wBAC3B,OAAO,EAAE,CAAC,CAAC,OAAO;wBAClB,QAAQ,EAAE,CAAC,CAAC,QAAQ;qBACrB,CAAC;oBACF,IAAI,CAAC,CAAC,SAAS,EAAE;wBACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;qBACvB;oBACD,aAAW,CAAC,wBAAyB,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBAChE,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,gEAAgE;aACjE;YAED,uBAAuB;YACvB,IAAM,eAAe,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAA,yBAAe,EAAC,aAAW,CAAC,IAAI,CAAC,CAAC,EAAlC,CAAkC,CAAC,CAAC;YACzF,eAAe,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC1B,aAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;;oBAAI,OAAA,iBAAe,CAAC,IAAI,WAAG,GAAC,IAAI,IAAG,CAAC,MAAG;gBAAnC,CAAmC,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YAEH,0FAA0F;YAC1F,IAAI,GAAG,IAAA,aAAG,EAAC,CAAC;oBACV,gBAAgB,EAAE,CAAC;4BACjB,KAAK,EAAE;gCACL,QAAQ,UAAA;gCACR,OAAO,EAAE,eAAS,CAAC,KAAK,CAAC,QAAQ;gCACjC,iBAAiB,EAAE,eAAS,CAAC,KAAK,CAAC,SAAS;gCAC5C,UAAU,EAAE,oCAAoC;6BACjD;yBACF,EAAE,EAAE,eAAe,mBAAA,EAAE,CAAC;iBACxB,CAAC,CAAC,CAAC;SAEL;QAED,iDAAiD;eACjD,kBAAM,IAAuB,EAAE;YAC7B;gBACE,GAAG,EAAE,iBAAiB;gBACtB,SAAS,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;gBAClD,UAAU,EAAE,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;aAC5D;YACD;gBACE,GAAG,EAAE,0BAA0B;gBAC/B,SAAS,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,0BAA0B,CAAC;gBAC9E,UAAU,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;aAC1D;SACF,CAAC;IAEJ,CAAC;IAED;;;MAGE;IACK,2CAAsB,GAA7B;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,MAAM,CAAC;IACnE,CAAC;IACD;;;MAGE;IACK,yCAAoB,GAA3B;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,mBAAmB,KAAK,MAAM,CAAC;IAClE,CAAC;IACD;;;;MAIE;IACK,gDAA2B,GAAlC,UAAmC,OAAe;QAChD,IAAI,IAAA,kBAAQ,EAAC,OAAO,CAAC,EAAE;YACrB,IAAI,UAAQ,CAAC;YACb,IAAM,UAAQ,GAAG,eAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,IAAA,yBAAe,EAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,EAAE;gBACvD,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAA,GAAG;oBAC5C,IAAI,GAAG,CAAC,OAAO,KAAK,UAAQ,EAAE;wBAC5B,UAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;wBACxB,OAAO;qBACR;gBACH,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,KAAK,UAAQ,EAAE;oBAC3D,UAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC;iBACxD;aACF;YACD,OAAO,UAAQ,CAAC;SACjB;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC;IAC5C,CAAC;IACH,iBAAC;AAAD,CAAC,AAvKD,CAAgC,kBAAQ,GAuKvC;AAvKY,gCAAU"}