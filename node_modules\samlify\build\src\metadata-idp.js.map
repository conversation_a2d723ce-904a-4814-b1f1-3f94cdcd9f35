{"version": 3, "file": "metadata-idp.js", "sourceRoot": "", "sources": ["../../src/metadata-idp.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;EAIE;AACF,wDAAyD;AAEzD,6BAAkC;AAClC,sDAAgC;AAChC,qCAAoE;AACpE,4CAAsB;AAMtB;;GAEG;AACH,mBAAwB,IAA4B;IAClD,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC;AAFD,4BAEC;AAED;;IAAiC,+BAAQ;IAEvC,qBAAY,IAA4B;QAEtC,IAAM,MAAM,GAAG,IAAA,kBAAQ,EAAC,IAAI,CAAC,IAAI,IAAI,YAAY,MAAM,CAAC;QAExD,IAAI,CAAC,MAAM,EAAE;YAEL,IAAA,KAQF,IAA0B,EAP5B,QAAQ,cAAA,EACR,WAAW,iBAAA,EACX,WAAW,iBAAA,EACX,+BAA+B,EAA/B,uBAAuB,mBAAG,KAAK,KAAA,EAC/B,oBAAiB,EAAjB,YAAY,mBAAG,EAAE,KAAA,EACjB,2BAAwB,EAAxB,mBAAmB,mBAAG,EAAE,KAAA,EACxB,2BAAwB,EAAxB,mBAAmB,mBAAG,EAAE,KACI,CAAC;YAE/B,IAAM,kBAAgB,GAAU,CAAC;oBAC/B,KAAK,EAAE;wBACL,uBAAuB,EAAE,MAAM,CAAC,uBAAuB,CAAC;wBACxD,0BAA0B,EAAE,eAAS,CAAC,KAAK,CAAC,QAAQ;qBACrD;iBACF,CAAC,CAAC;;gBAEH,KAAkB,IAAA,KAAA,SAAA,IAAA,sBAAY,EAAC,WAAW,CAAC,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,IAAI,WAAA;oBACZ,kBAAgB,CAAC,IAAI,CAAC,iBAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;iBAClE;;;;;;;;;;gBAED,KAAkB,IAAA,KAAA,SAAA,IAAA,sBAAY,EAAC,WAAW,CAAC,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,IAAI,WAAA;oBACZ,kBAAgB,CAAC,IAAI,CAAC,iBAAO,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;iBACrE;;;;;;;;;YAED,IAAI,IAAA,yBAAe,EAAC,YAAY,CAAC,EAAE;gBACjC,YAAY,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,kBAAgB,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC,EAA1C,CAA0C,CAAC,CAAC;aACvE;YAED,IAAI,IAAA,yBAAe,EAAC,mBAAmB,CAAC,EAAE;gBACxC,mBAAmB,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,UAAU;oBACxC,IAAM,IAAI,GAAQ;wBAChB,OAAO,EAAE,CAAC,CAAC,OAAO;wBAClB,QAAQ,EAAE,CAAC,CAAC,QAAQ;qBACrB,CAAC;oBACF,IAAI,CAAC,CAAC,SAAS,EAAE;wBACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;qBACvB;oBACD,kBAAgB,CAAC,IAAI,CAAC,EAAE,mBAAmB,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;aACpE;YAED,IAAI,IAAA,yBAAe,EAAC,mBAAmB,CAAC,EAAE;gBACxC,mBAAmB,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,UAAU;oBACxC,IAAM,IAAI,GAAQ,EAAE,CAAC;oBACrB,IAAI,CAAC,CAAC,SAAS,EAAE;wBACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;qBACvB;oBACD,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;oBACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;oBAC3B,kBAAgB,CAAC,IAAI,CAAC,EAAE,mBAAmB,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;aACxF;YACD,mCAAmC;YACnC,IAAI,GAAG,IAAA,aAAG,EAAC,CAAC;oBACV,gBAAgB,EAAE,CAAC;4BACjB,KAAK,EAAE;gCACL,OAAO,EAAE,eAAS,CAAC,KAAK,CAAC,QAAQ;gCACjC,iBAAiB,EAAE,eAAS,CAAC,KAAK,CAAC,SAAS;gCAC5C,UAAU,EAAE,oCAAoC;gCAChD,QAAQ,UAAA;6BACT;yBACF,EAAE,EAAE,gBAAgB,oBAAA,EAAE,CAAC;iBACzB,CAAC,CAAC,CAAC;SACL;eAED,kBAAM,IAAuB,EAAE;YAC7B;gBACE,GAAG,EAAE,yBAAyB;gBAC9B,SAAS,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;gBACnD,UAAU,EAAE,CAAC,yBAAyB,CAAC;aACxC;YACD;gBACE,GAAG,EAAE,qBAAqB;gBAC1B,SAAS,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,qBAAqB,CAAC;gBAC1E,KAAK,EAAE,CAAC,SAAS,CAAC;gBAClB,aAAa,EAAE,EAAE;gBACjB,UAAU,EAAE,CAAC,UAAU,CAAC;aACzB;SACF,CAAC;IAEJ,CAAC;IAED;;;MAGE;IACF,+CAAyB,GAAzB;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC;QAC9C,IAAI,GAAG,KAAK,SAAS,EAAE;YACrB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC;IAChC,CAAC;IAED;;;;MAIE;IACF,4CAAsB,GAAtB,UAAuB,OAAe;QACpC,IAAI,IAAA,kBAAQ,EAAC,OAAO,CAAC,EAAE;YACrB,IAAM,QAAQ,GAAG,eAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,OAAO,EAAE;gBACX,OAAO,OAAO,CAAC;aAChB;SACF;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC;IACvC,CAAC;IACH,kBAAC;AAAD,CAAC,AA1HD,CAAiC,kBAAQ,GA0HxC;AA1HY,kCAAW"}