import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@aws-sdk/protocol-http";
import { SerdeContext as __SerdeContext } from "@aws-sdk/types";
import { AssumeRoleCommandInput, AssumeRoleCommandOutput } from "../commands/AssumeRoleCommand";
import { AssumeRoleWithSAMLCommandInput, AssumeRoleWithSAMLCommandOutput } from "../commands/AssumeRoleWithSAMLCommand";
import { AssumeRoleWithWebIdentityCommandInput, AssumeRoleWithWebIdentityCommandOutput } from "../commands/AssumeRoleWithWebIdentityCommand";
import { DecodeAuthorizationMessageCommandInput, DecodeAuthorizationMessageCommandOutput } from "../commands/DecodeAuthorizationMessageCommand";
import { GetAccessKeyInfoCommandInput, GetAccessKeyInfoCommandOutput } from "../commands/GetAccessKeyInfoCommand";
import { GetCallerIdentityCommandInput, GetCallerIdentityCommandOutput } from "../commands/GetCallerIdentityCommand";
import { GetFederationTokenCommandInput, GetFederationTokenCommandOutput } from "../commands/GetFederationTokenCommand";
import { GetSessionTokenCommandInput, GetSessionTokenCommandOutput } from "../commands/GetSessionTokenCommand";
/**
 * serializeAws_queryAssumeRoleCommand
 */
export declare const se_AssumeRoleCommand: (input: AssumeRoleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryAssumeRoleWithSAMLCommand
 */
export declare const se_AssumeRoleWithSAMLCommand: (input: AssumeRoleWithSAMLCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryAssumeRoleWithWebIdentityCommand
 */
export declare const se_AssumeRoleWithWebIdentityCommand: (input: AssumeRoleWithWebIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryDecodeAuthorizationMessageCommand
 */
export declare const se_DecodeAuthorizationMessageCommand: (input: DecodeAuthorizationMessageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryGetAccessKeyInfoCommand
 */
export declare const se_GetAccessKeyInfoCommand: (input: GetAccessKeyInfoCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryGetCallerIdentityCommand
 */
export declare const se_GetCallerIdentityCommand: (input: GetCallerIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryGetFederationTokenCommand
 */
export declare const se_GetFederationTokenCommand: (input: GetFederationTokenCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryGetSessionTokenCommand
 */
export declare const se_GetSessionTokenCommand: (input: GetSessionTokenCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_queryAssumeRoleCommand
 */
export declare const de_AssumeRoleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssumeRoleCommandOutput>;
/**
 * deserializeAws_queryAssumeRoleWithSAMLCommand
 */
export declare const de_AssumeRoleWithSAMLCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssumeRoleWithSAMLCommandOutput>;
/**
 * deserializeAws_queryAssumeRoleWithWebIdentityCommand
 */
export declare const de_AssumeRoleWithWebIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssumeRoleWithWebIdentityCommandOutput>;
/**
 * deserializeAws_queryDecodeAuthorizationMessageCommand
 */
export declare const de_DecodeAuthorizationMessageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DecodeAuthorizationMessageCommandOutput>;
/**
 * deserializeAws_queryGetAccessKeyInfoCommand
 */
export declare const de_GetAccessKeyInfoCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetAccessKeyInfoCommandOutput>;
/**
 * deserializeAws_queryGetCallerIdentityCommand
 */
export declare const de_GetCallerIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetCallerIdentityCommandOutput>;
/**
 * deserializeAws_queryGetFederationTokenCommand
 */
export declare const de_GetFederationTokenCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetFederationTokenCommandOutput>;
/**
 * deserializeAws_queryGetSessionTokenCommand
 */
export declare const de_GetSessionTokenCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetSessionTokenCommandOutput>;
