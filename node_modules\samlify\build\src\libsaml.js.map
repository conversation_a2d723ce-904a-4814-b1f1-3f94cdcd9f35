{"version": 3, "file": "libsaml.js", "sourceRoot": "", "sources": ["../../src/libsaml.ts"], "names": [], "mappings": ";AAAA;;;;EAIE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEF,yCAA2C;AAC3C,mDAA2D;AAC3D,6BAAuD;AACvD,+BAA+B;AAE/B,sDAAmD;AACnD,yCAAoD;AACpD,+DAAmD;AACnD,yCAAsC;AACtC,wDAAkC;AAClC,6BAAmC;AAEnC,IAAM,mBAAmB,GAAG,gBAAU,CAAC,SAAS,CAAC;AACjD,IAAM,gBAAgB,GAAG,gBAAU,CAAC,MAAM,CAAC;AAC3C,IAAM,OAAO,GAAG,aAAO,CAAC,OAAO,CAAC;AAChC,IAAM,SAAS,GAAG,aAAO,CAAC,SAAS,CAAC;AACpC,IAAM,GAAG,GAAG,kBAAS,CAAC;AA6FtB,IAAM,OAAO,GAAG;IAEd;;;MAGE;IACF,SAAS,mBAAmB,CAAC,IAAY;QACvC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACzE,OAAO,aAAa,CAAC;SACtB;QACD,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC3E,OAAO,cAAc,CAAC;SACvB;QACD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;IACD;;OAEG;IACH,IAAM,gBAAgB,GAAG;QACvB,4CAA4C,EAAE,YAAY;QAC1D,mDAAmD,EAAE,cAAc;QACnE,mDAAmD,EAAE,cAAc;KACpE,CAAC;IACF;;;MAGE;IACF,IAAM,2BAA2B,GAAG;QAClC,OAAO,EAAE,4cAA4c;KACtd,CAAC;IACF;;;MAGE;IACF,IAAM,4BAA4B,GAAG;QACnC,OAAO,EAAE,mUAAmU;KAC7U,CAAC;IAEF;;;MAGE;IACF,IAAM,iCAAiC,GAAG;QACxC,OAAO,EAAE,iEAAiE;KAC3E,CAAC;IAEF;;;MAGE;IACF,IAAM,wBAAwB,GAAG;QAC/B,OAAO,EAAE,6MAA6M;KACvN,CAAC;IAEF;;;MAGE;IACF,IAAM,4BAA4B,GAAG;QACnC,OAAO,EAAE,qrCAAqrC;QAC9rC,UAAU,EAAE,EAAE;QACd,mBAAmB,EAAE;YACnB,4BAA4B,EAAE,iCAAiC;YAC/D,mBAAmB,EAAE,wBAAwB;SAC9C;KACF,CAAC;IACF;;;MAGE;IACF,IAAM,6BAA6B,GAAG;QACpC,OAAO,EAAE,6WAA6W;KACvX,CAAC;IACF;;;;;MAKE;IACF,SAAS,gBAAgB,CAAC,MAAe;QACvC,IAAI,MAAM,EAAE;YACV,IAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE;gBAC7B,OAAO,QAAQ,CAAC;aACjB;SACF;QACD,OAAO,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IACD;;;;;MAKE;IACF,SAAS,eAAe,CAAC,MAAc;QACrC,IAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,EAAE;YAC9B,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,IAAI,CAAC,CAAC,gBAAgB;IAC/B,CAAC;IACD;;;;;;MAME;IACF,SAAS,WAAW,CAAC,KAAK,EAAE,YAAsB;QAChD,IAAI,IAAA,kBAAQ,EAAC,KAAK,CAAC,EAAE;YACnB,OAAO,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,qBAAqB,GAAG,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,qBAAqB,GAAG,KAAK,GAAG,IAAI,CAAC;SACnH;QACD,OAAO,qBAAqB,GAAG,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;IAClE,CAAC;IAED;;;;;;OAMG;IACH,SAAS,OAAO,CAAC,MAAc,EAAE,OAAe;QAC9C,IAAM,YAAY,GAAG,IAAA,mBAAS,EAAC,OAAO,EAAE,EAAC,MAAM,EAAE,OAAO,EAAC,CAAC,CAAC;QAC3D,OAAO,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/E,CAAC;IAED,OAAO;QAEL,WAAW,aAAA;QACX,mBAAmB,qBAAA;QACnB,2BAA2B,6BAAA;QAC3B,4BAA4B,8BAAA;QAC5B,iCAAiC,mCAAA;QACjC,wBAAwB,0BAAA;QACxB,4BAA4B,8BAAA;QAC5B,6BAA6B,+BAAA;QAE7B;;;;;UAKE;QACF,kBAAkB,YAAC,MAAc,EAAE,SAAc;YAC/C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;gBAC9B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,WAAI,CAAC,MAAG,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QACD;;;;;;UAME;QACF,yBAAyB,YACvB,UAAoC,EACpC,iBAA+D,EAC/D,0BAA0F;YAD1F,kCAAA,EAAA,4CAA+D;YAC/D,2CAAA,EAAA,8DAA0F;YAE1F,IAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,UAAC,EAAyE;oBAAvE,IAAI,UAAA,EAAE,UAAU,gBAAA,EAAE,QAAQ,cAAA,EAAE,YAAY,kBAAA,EAAE,YAAY,kBAAA,EAAE,aAAa,mBAAA;gBAClG,IAAM,mBAAmB,GAAG,kCAAkC,CAAC;gBAC/D,IAAM,oBAAoB,GAAG,2CAA2C,CAAC;gBACzE,IAAI,aAAa,GAAG,iBAAiB,CAAC,OAAO,CAAC;gBAC9C,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACtD,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;gBAClE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC;gBAC3G,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;gBAC/G,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;gBACtE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,WAAI,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAG,CAAC,CAAC;gBACnF,OAAO,aAAa,CAAC;YACvB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,OAAO,0BAA0B,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC1E,CAAC;QAED;;;;;;;;;;UAUE;QACF,sBAAsB,YAAC,IAA0B;YAE7C,IAAA,cAAc,GAaZ,IAAI,eAbQ,EACd,iBAAiB,GAYf,IAAI,kBAZW,EACjB,UAAU,GAWR,IAAI,WAXI,EACV,cAAc,GAUZ,IAAI,eAVQ,EACd,KASE,IAAI,mBAT6C,EAAnD,kBAAkB,mBAAG,mBAAmB,CAAC,UAAU,KAAA,EACnD,KAQE,IAAI,yBALL,EAHD,wBAAwB,mBAAG;gBACzB,uDAAuD;gBACvD,yCAAyC;aAC1C,KAAA,EACD,WAAW,GAIT,IAAI,YAJK,EACX,eAAe,GAGb,IAAI,gBAHS,EACf,KAEE,IAAI,eAFe,EAArB,cAAc,mBAAG,IAAI,KAAA,EACrB,KACE,IAAI,gBADiB,EAAvB,eAAe,mBAAG,KAAK,KAAA,CAChB;YACT,IAAM,GAAG,GAAG,IAAI,sBAAS,EAAE,CAAC;YAC5B,sCAAsC;YACtC,IAAI,iBAAiB,EAAE;gBACrB,GAAG,CAAC,YAAY,CACd,iBAAiB,EACjB,wBAAwB,EACxB,eAAe,CAAC,kBAAkB,CAAC,CACpC,CAAC;aACH;YACD,IAAI,eAAe,EAAE;gBACnB,GAAG,CAAC,YAAY;gBACd,6BAA6B;gBAC7B,IAAI,EACJ,wBAAwB,EACxB,eAAe,CAAC,kBAAkB,CAAC,EACnC,EAAE,EACF,EAAE,EACF,EAAE,EACF,KAAK,CACN,CAAC;aACH;YACD,GAAG,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC5C,GAAG,CAAC,eAAe,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YACxE,GAAG,CAAC,UAAU,GAAG,iBAAO,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;YAC1E,IAAI,eAAe,EAAE;gBACnB,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;aACvD;iBAAM;gBACL,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;aACtC;YACD,OAAO,cAAc,KAAK,KAAK,CAAC,CAAC,CAAC,iBAAO,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QAClG,CAAC;QACD;;;;;UAKE;QACF,eAAe,YAAC,GAAW,EAAE,IAA8B;YAA3D,iBA+IC;YA7IC,IAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC3C,iIAAiI;YACjI,sDAAsD;YACtD,IAAM,qBAAqB,GAAG,0GAA0G,CAAC;YACzI,wDAAwD;YACxD,IAAM,uBAAuB,GAAG,uIAAuI,CAAC;YACxK,6DAA6D;YAC7D,IAAM,qBAAqB,GAAG,6OAA6O,CAAC;YAE5Q,4BAA4B;YAC5B,IAAI,SAAS,GAAQ,EAAE,CAAC;YACxB,IAAI,aAAa,GAAkB,IAAI,CAAC;YACxC,IAAM,oBAAoB,GAAG,IAAA,cAAM,EAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;YAChE,IAAM,sBAAsB,GAAG,IAAA,cAAM,EAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACpE,IAAM,mBAAmB,GAAG,IAAA,cAAM,EAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;YAE/D,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YACnD,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;YAErD,yCAAyC;YACzC,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;aAClD;YAED,iDAAiD;YACjD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;aACvC;YAED,IAAM,GAAG,GAAG,IAAI,sBAAS,EAAE,CAAC;YAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC;YACpB,4BAA4B;YAC5B,SAAS,CAAC,OAAO,CAAC,UAAA,aAAa;gBAE7B,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAEjD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACnC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;iBAC7D;gBAED,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,GAAG,CAAC,eAAe,GAAG,IAAI,wBAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrD;gBAED,IAAI,IAAI,CAAC,QAAQ,EAAE;oBAEjB,IAAM,eAAe,GAAG,IAAA,cAAM,EAAC,uCAAuC,EAAE,aAAa,CAAQ,CAAC;oBAC9F,0BAA0B;oBAC1B,IAAI,YAAY,GAAQ,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAC1E,oEAAoE;oBACpE,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;wBAC/B,YAAY,GAAG,IAAA,qBAAW,EAAC,YAAY,CAAC,CAAC;qBAC1C;yBAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;wBAC3C,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;qBAC/B;oBACD,mCAAmC;oBACnC,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,iBAAO,CAAC,kBAAkB,CAAC,CAAC;oBAE5D,gDAAgD;oBAChD,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC7D,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;qBAC5C;oBAED,+BAA+B;oBAC/B,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;wBAChC,IAAM,mBAAmB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;wBAC/D,IAAM,iBAAe,GAAG,iBAAO,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;wBAExE,IACE,YAAY,CAAC,MAAM,IAAI,CAAC;4BACxB,CAAC,YAAY,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,EAAE,KAAK,iBAAe,CAAC,IAAI,EAAE,EAAtC,CAAsC,CAAC,EAClE;4BACA,sDAAsD;4BACtD,8EAA8E;4BAC9E,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;yBACtE;wBAED,GAAG,CAAC,eAAe,GAAG,IAAI,KAAI,CAAC,UAAU,CAAC,iBAAe,CAAC,CAAC;qBAE5D;yBAAM;wBACL,iCAAiC;wBACjC,GAAG,CAAC,eAAe,GAAG,IAAI,KAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC5D;iBAEF;gBAED,GAAG,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBAEjC,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBAE/B,QAAQ,GAAG,QAAQ,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAE1D,kFAAkF;gBAClF,IAAI,CAAC,QAAQ,EAAE;oBACb,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;iBACnD;YAEH,CAAC,CAAC,CAAC;YAEH,+DAA+D;YAC/D,wDAAwD;YACxD,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrC,IAAM,IAAI,GAAG,IAAA,cAAM,EAAC,0GAA0G,EAAE,GAAG,CAAC,CAAC;gBACrI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;iBACpC;aACF;YAED,IAAI,sBAAsB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvC,IAAM,qBAAqB,GAAG,IAAA,mBAAO,EAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;wBAC3E,GAAG,EAAE,QAAQ;wBACb,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;wBACnD,UAAU,EAAE,CAAC,KAAK,CAAC;qBACpB,CAAC,CAAC,CAAC;gBACJ,8DAA8D;gBAC9D,IAAM,oBAAoB,GAAG,IAAA,mBAAO,EAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;wBACpD,GAAG,EAAE,IAAI;wBACT,SAAS,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;wBACrC,UAAU,EAAE,CAAC,IAAI,CAAC;qBACnB,CAAC,CAAC,CAAC;gBACJ,mBAAmB;gBACnB,wGAAwG;gBACxG,yGAAyG;gBACzG,wGAAwG;gBACxG,gEAAgE;gBAChE,iGAAiG;gBACjG,6GAA6G;gBAC7G,oGAAoG;gBACpG,IAAI,qBAAqB,CAAC,MAAM,KAAK,WAAI,oBAAoB,CAAC,EAAE,CAAE,EAAE;oBAClE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;iBAClD;gBACD,IAAM,WAAW,GAAG,IAAA,mBAAO,EAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;wBAC3C,GAAG,EAAE,WAAW;wBAChB,SAAS,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;wBACrC,UAAU,EAAE,EAAE;wBACd,OAAO,EAAE,IAAI;qBACd,CAAC,CAAC,CAAC;gBACJ,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;aAClD;YAED,OAAO,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QACnC,CAAC;QACD;;;;;UAKE;QACF,gBAAgB,YAAC,GAAW,EAAE,UAA2B;;YACvD;gBACE,GAAC,eAAe,IAAG;oBACjB;wBACE,KAAK,EAAE,EAAE,GAAG,KAAA,EAAE;qBACf;;wBAEC,GAAC,YAAY,IAAG;4BACd;gCACE,KAAK,EAAE;oCACL,UAAU,EAAE,oCAAoC;iCACjD;6BACF;;gCAEC,GAAC,aAAa,IAAG,CAAC;wCAChB,oBAAoB,EAAE,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC;qCAC7D,CAAC;;yBAEL;;iBACD;mBACJ;QACJ,CAAC;QACD;;;;;;;UAOE;QACF,yBAAyB,YACvB,WAAmB,EACnB,GAAW,EACX,UAAmB,EACnB,QAAkB,EAClB,gBAAyB;YAEzB,6CAA6C;YAC7C,6BAA6B;YAC7B,IAAM,YAAY,GAAG,IAAI,kBAAI,CAC3B,iBAAO,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,CAAC,EACvC,SAAS,EACT;gBACE,aAAa,EAAE,gBAAgB,CAAC,gBAAgB,CAAC;aAClD,CACF,CAAC;YACF,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,+BAA+B;YAC/B,OAAO,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACvE,CAAC;QACD;;;;;;;UAOE;QACF,sBAAsB,YACpB,QAAQ,EACR,WAAmB,EACnB,SAA0B,EAC1B,eAAwB;YAExB,IAAM,QAAQ,GAAG,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9D,IAAM,aAAa,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;YACxD,IAAM,GAAG,GAAG,IAAI,kBAAI,CAAC,iBAAO,CAAC,8BAA8B,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,aAAa,eAAA,EAAE,CAAC,CAAC;YACpG,OAAO,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACtE,CAAC;QACD;;;;UAIE;QACF,UAAU,YAAC,eAAuB,EAAE,eAAyB;YAAzB,gCAAA,EAAA,oBAAyB;YAC3D,IAAI,CAAC,UAAU,GAAG,UAAA,GAAG;gBACnB,IAAM,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,UAAG,eAAe,CAAC,MAAM,MAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1E,OAAO,WAAI,MAAM,uBAAa,MAAM,6BAAmB,eAAe,eAAK,MAAM,+BAAqB,MAAM,cAAW,CAAC;YAC1H,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,GAAG,UAAA,OAAO;gBACnB,OAAO,iBAAO,CAAC,8BAA8B,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC5E,CAAC,CAAC;QACJ,CAAC;QACD;;;;;;UAME;QACF,gBAAgB,YAAC,YAAY,EAAE,YAAY,EAAE,GAAY;YACvD,iDAAiD;YACjD,OAAO,IAAI,OAAO,CAAS,UAAC,OAAO,EAAE,MAAM;gBAEzC,IAAI,CAAC,GAAG,EAAE;oBACR,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;iBACrD;gBAED,IAAM,mBAAmB,GAAG,YAAY,CAAC,aAAa,CAAC;gBACvD,IAAM,oBAAoB,GAAG,YAAY,CAAC,UAAU,CAAC;gBACrD,IAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC3C,IAAM,UAAU,GAAG,IAAA,cAAM,EAAC,gCAAgC,EAAE,GAAG,CAAW,CAAC;gBAC3E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;iBACrC;gBACD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;iBAC3C;gBACD,IAAM,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAEvC,8DAA8D;gBAC9D,IAAI,mBAAmB,CAAC,oBAAoB,EAAE;oBAE5C,IAAM,YAAY,GAAG,iBAAO,CAAC,8BAA8B,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;oBAEtH,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE;wBAC1C,4BAA4B;wBAC5B,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;wBAClC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,qCAA8B,oBAAoB,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,8BAA2B,CAAC;wBACnI,mBAAmB,EAAE,mBAAmB,CAAC,uBAAuB;wBAChE,sBAAsB,EAAE,mBAAmB,CAAC,sBAAsB;qBACnE,EAAE,UAAC,GAAG,EAAE,GAAG;wBACV,IAAI,GAAG,EAAE;4BACP,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BACnB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAC;yBACnE;wBACD,IAAI,CAAC,GAAG,EAAE;4BACR,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;yBAC/D;wBACO,IAAoB,kBAAkB,GAAK,mBAAmB,CAAC,SAAS,mBAAlC,CAAmC;wBACjF,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,WAAI,kBAAkB,uCAA6B,kBAAkB,gBAAK,eAAS,CAAC,KAAK,CAAC,SAAS,gBAAK,GAAG,eAAK,kBAAkB,yBAAsB,CAAC,CAAC;wBAChN,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;wBACxF,OAAO,OAAO,CAAC,iBAAO,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;oBACvD,CAAC,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO,OAAO,CAAC,iBAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,2BAA2B;iBACvE;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QACD;;;;;;;UAOE;QACF,gBAAgB,YAAC,IAAI,EAAE,SAAiB;YACtC,OAAO,IAAI,OAAO,CAAgB,UAAC,OAAO,EAAE,MAAM;gBAChD,sDAAsD;gBACtD,IAAI,CAAC,SAAS,EAAE;oBACd,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC;iBACrD;gBACD,2FAA2F;gBAC3F,IAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;gBACvC,IAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBACjD,IAAM,mBAAmB,GAAG,IAAA,cAAM,EAAC,8EAA8E,EAAE,GAAG,CAAW,CAAC;gBAClI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC3E,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;iBACtD;gBACD,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClC,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;iBAC3C;gBACD,IAAM,gBAAgB,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBAEhD,OAAO,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE;oBACjD,GAAG,EAAE,iBAAO,CAAC,cAAc,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,iBAAiB,CAAC;iBACtF,EAAE,UAAC,GAAG,EAAE,GAAG;oBACV,IAAI,GAAG,EAAE;wBACP,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACnB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAC;qBACnE;oBACD,IAAI,CAAC,GAAG,EAAE;wBACR,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;qBAC/D;oBACD,IAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBACvD,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;oBACpF,OAAO,OAAO,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QACD;;WAEG;QACG,UAAU,YAAC,KAAa;;;;;;4BAGpB,QAAQ,GAAK,IAAA,gBAAU,GAAE,SAAjB,CAAkB;4BAElC;;;;;+BAKG;4BACH,IAAI,CAAC,QAAQ,EAAE;gCAEb,qCAAqC;gCACrC,sBAAO,OAAO,CAAC,MAAM,CAAC,+LAA+L,CAAC,EAAC;6BAExN;;;;4BAGQ,qBAAM,QAAQ,CAAC,KAAK,CAAC,EAAA;gCAA5B,sBAAO,SAAqB,EAAC;;;4BAE7B,MAAM,GAAC,CAAC;;;;;SAGX;KACF,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,OAAO,EAAE,CAAC"}