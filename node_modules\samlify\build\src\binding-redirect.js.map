{"version": 3, "file": "binding-redirect.js", "sourceRoot": "", "sources": ["../../src/binding-redirect.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;EAIE;AACF,mDAAyC;AACzC,sDAAgC;AAIhC,uCAA2B;AAC3B,6BAA2C;AAE3C,IAAM,OAAO,GAAG,aAAO,CAAC,OAAO,CAAC;AAChC,IAAM,SAAS,GAAG,aAAO,CAAC,SAAS,CAAC;AAWpC;;;;;;;EAOE;AACF,SAAS,MAAM,CAAC,KAAa,EAAE,KAAa,EAAE,KAAe;IAC3D,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC;AAC5D,CAAC;AACD;;;;;;;;EAQE;AACF,SAAS,gBAAgB,CAAC,IAAyB;IAE/C,IAAA,OAAO,GAKL,IAAI,QALC,EACP,IAAI,GAIF,IAAI,KAJF,EACJ,QAAQ,GAGN,IAAI,SAHE,EACR,OAAO,GAEL,IAAI,QAFC,EACP,aAAa,GACX,IAAI,cADO,CACN;IACH,IAAA,KAAoB,IAAI,WAAT,EAAf,UAAU,mBAAG,EAAE,KAAA,CAAU;IAC/B,IAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;IAC/D,IAAM,UAAU,GAAG,iBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACrD,8EAA8E;IAC9E,IAAM,WAAW,GAAG,kBAAkB,CAAC,iBAAO,CAAC,YAAY,CAAC,iBAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,UAAU,KAAK,EAAE,EAAE;QACrB,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;KAC3E;IACD,IAAI,QAAQ,EAAE;QACZ,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,kBAAkB,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACrG,IAAM,WAAW,GAAG,WAAW,GAAG,UAAU,GAAG,MAAM,CAAC;QACtD,OAAO,OAAO;cACV,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC;cACzC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,kBAAkB,CAC9C,iBAAO,CAAC,yBAAyB,CAC/B,UAAU,GAAG,GAAG,GAAG,WAAW,EAC9B,aAAa,CAAC,UAAU,EACxB,aAAa,CAAC,cAAc,EAC5B,SAAS,EACT,aAAa,CAAC,yBAAyB,CACxC,CAAC,QAAQ,EAAE,CACb,CACA,CAAC;KACL;IACD,OAAO,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,WAAW,GAAG,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC1E,CAAC;AACD;;;;;EAKE;AACF,SAAS,uBAAuB,CAAC,MAA4B,EAAE,oBAA2D;IAExH,IAAM,QAAQ,GAAQ,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;IAC/E,IAAM,SAAS,GAAQ,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC;IAC/C,IAAI,EAAE,GAAW,EAAE,CAAC;IAEpB,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE;QAC3C,IAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,cAAc,SAAQ,CAAC;QAC3B,IAAI,SAAS,CAAC,oBAAoB,IAAI,oBAAoB,EAAE;YAC1D,IAAM,IAAI,GAAG,oBAAoB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YAClE,EAAE,GAAG,IAAA,aAAG,EAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3B,cAAc,GAAG,IAAA,aAAG,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAC7C;aAAM;YACL,IAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;YAC5C,IAAM,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC1F,EAAE,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YAC5B,cAAc,GAAG,iBAAO,CAAC,kBAAkB,CAAC,iBAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE;gBACvF,EAAE,EAAE,EAAE;gBACN,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;gBACjC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,YAAY,EAAE,oBAAoB;gBAClC,2BAA2B,EAAE,QAAQ,CAAC,EAAE,CAAC,2BAA2B,CAAC,OAAO,CAAC,IAAI,CAAC;gBAClF,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,SAAS,CAAC,WAAW;aAC5B,CAAC,CAAC;SACX;QACD,OAAO;YACL,EAAE,IAAA;YACF,OAAO,EAAE,gBAAgB,CAAC;gBACxB,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE,SAAS,CAAC,WAAW;gBAC3B,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,oBAAoB,EAAE;gBAC5C,aAAa,EAAE,SAAS;gBACxB,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,SAAS,CAAC,UAAU;aACjC,CAAC;SACH,CAAC;KACH;IACD,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AAC1E,CAAC;AAED;;;;;;;EAOE;AACF,SAAS,wBAAwB,CAAC,WAAgB,EAAE,MAAW,EAAE,IAAc,EAAE,UAAmB,EAAE,oBAA2D;IAAhG,qBAAA,EAAA,SAAc;IAC7E,IAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC;IAC5C,IAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC;IAC1C,IAAM,QAAQ,GAAG;QACf,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU;QAC1B,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU;KACzB,CAAC;IAEF,IAAI,EAAE,GAAW,UAAU,CAAC,UAAU,EAAE,CAAC;IACzC,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE;QAC3C,IAAM,IAAI,GAAG,QAAQ,CAAC,EAAE,CAAC,2BAA2B,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACvE,IAAI,eAAe,SAAQ,CAAC;QAC5B,EAAE;QACF,IAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,IAAM,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QAC1F,IAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,kEAAkE;QAClE,IAAM,oBAAoB,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,MAAO,CAAC,CAAC;QACnE,IAAM,MAAM,GAAQ;YAClB,EAAE,EAAE,EAAE;YACN,WAAW,EAAE,UAAU,CAAC,UAAU,EAAE;YACpC,WAAW,EAAE,IAAI;YACjB,gBAAgB,EAAE,IAAI;YACtB,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;YAClC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE;YACnC,YAAY,EAAE,OAAO,CAAC,WAAW,EAAE;YACnC,2BAA2B,EAAE,IAAI;YACjC,UAAU,EAAE,eAAS,CAAC,UAAU,CAAC,OAAO;YACxC,oBAAoB;YACpB,mBAAmB,EAAE,OAAO,CAAC,WAAW,EAAE;YAC1C,sBAAsB,EAAE,oBAAoB,CAAC,WAAW,EAAE;YAC1D,mCAAmC,EAAE,oBAAoB,CAAC,WAAW,EAAE;YACvE,YAAY,EAAE,oBAAoB;YAClC,MAAM,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;YACxB,YAAY,EAAE,IAAA,aAAG,EAAC,WAAW,EAAE,oBAAoB,EAAE,EAAE,CAAC;YACxD,cAAc,EAAE,EAAE;YAClB,kBAAkB,EAAE,EAAE;SACvB,CAAC;QAEF,IAAI,UAAU,CAAC,qBAAqB,IAAI,oBAAoB,EAAE;YAC5D,IAAM,QAAQ,GAAG,oBAAoB,CAAC,UAAU,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAChF,EAAE,GAAG,IAAA,aAAG,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,eAAe,GAAG,IAAA,aAAG,EAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAClD;aAAM;YAEL,IAAI,WAAW,KAAK,IAAI,EAAE;gBACxB,MAAM,CAAC,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;aACtD;YACD,eAAe,GAAG,iBAAO,CAAC,kBAAkB,CAAC,iBAAO,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACpG;QAEO,IAAA,UAAU,GAAoE,UAAU,WAA9E,EAAE,cAAc,GAAoD,UAAU,eAA9D,EAA6B,kBAAkB,GAAK,UAAU,0BAAf,CAAgB;QACjG,IAAM,MAAM,GAAG;YACb,UAAU,YAAA;YACV,cAAc,gBAAA;YACd,kBAAkB,oBAAA;YAClB,WAAW,EAAE,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACvD,cAAc,EAAE,KAAK;SACtB,CAAC;QACF,0DAA0D;QAC1D,IAAI,QAAQ,CAAC,EAAE,CAAC,sBAAsB,EAAE,EAAE;YACxC,eAAe,GAAG,iBAAO,CAAC,sBAAsB,uBAC3C,MAAM,KACT,cAAc,EAAE,eAAe,EAC/B,wBAAwB,EAAE,SAAS,CAAC,wBAAwB,EAC5D,iBAAiB,EAAE,2DAA2D,EAC9E,eAAe,EAAE;oBACf,MAAM,EAAE,IAAI;oBACZ,QAAQ,EAAE,EAAE,SAAS,EAAE,qFAAqF,EAAE,MAAM,EAAE,OAAO,EAAE;iBAChI,IACD,CAAC;SACJ;QAED,uDAAuD;QACvD,OAAO;YACL,EAAE,IAAA;YACF,OAAO,EAAE,gBAAgB,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,SAAS,CAAC,YAAY;gBAC5B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,aAAa,EAAE,UAAU;gBACzB,UAAU,YAAA;aACX,CAAC;SACH,CAAC;KACH;IACD,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;EAME;AACF,SAAS,wBAAwB,CAAC,IAAI,EAAE,MAAM,EAAE,UAAmB,EAAE,oBAAyE;IAC5I,IAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IACpF,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;IAC9C,IAAI,EAAE,GAAW,WAAW,CAAC,UAAU,EAAE,CAAC;IAC1C,IAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IAC9C,IAAM,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAE1F,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE;QAChD,IAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtE,IAAI,cAAc,GAAW,EAAE,CAAC;QAChC,IAAM,YAAY,GAAG;YACnB,EAAE,EAAE,EAAE;YACN,WAAW,EAAE,IAAI;YACjB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;YACnC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACtC,YAAY,EAAE,oBAAoB;YAClC,MAAM,EAAE,IAAI,CAAC,YAAY;YACzB,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAC;QACF,IAAI,WAAW,CAAC,qBAAqB,IAAI,oBAAoB,EAAE;YAC7D,IAAM,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;YACnF,EAAE,GAAG,IAAA,aAAG,EAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3B,cAAc,GAAG,IAAA,aAAG,EAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAC7C;aAAM;YACL,cAAc,GAAG,iBAAO,CAAC,kBAAkB,CAAC,iBAAO,CAAC,4BAA4B,CAAC,OAAO,EAAE,YAAmB,CAAC,CAAC;SAChH;QACD,OAAO;YACL,EAAE,IAAA;YACF,OAAO,EAAE,gBAAgB,CAAC;gBACxB,OAAO,EAAE,cAAc;gBACvB,UAAU,YAAA;gBACV,IAAI,EAAE,SAAS,CAAC,aAAa;gBAC7B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,uBAAuB;gBAC7D,aAAa,EAAE,WAAW;gBAC1B,OAAO,EAAE,IAAI;aACd,CAAC;SACH,CAAC;KACH;IACD,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC3E,CAAC;AACD;;;;;EAKE;AACF,SAAS,yBAAyB,CAAC,WAAgB,EAAE,MAAW,EAAE,UAAmB,EAAE,oBAA2D;IAChJ,IAAM,QAAQ,GAAG;QACf,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU;QAC5B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU;KACjC,CAAC;IACF,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;IAC9C,IAAI,EAAE,GAAW,WAAW,CAAC,UAAU,EAAE,CAAC;IAC1C,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE;QAChD,IAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtE,IAAI,eAAe,SAAQ,CAAC;QAC5B,IAAI,WAAW,CAAC,sBAAsB,IAAI,oBAAoB,EAAE;YAC9D,IAAM,QAAQ,GAAG,oBAAoB,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;YAC1E,EAAE,GAAG,IAAA,aAAG,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,eAAe,GAAG,IAAA,aAAG,EAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAClD;aAAM;YACL,IAAM,MAAM,GAAQ;gBAClB,EAAE,EAAE,EAAE;gBACN,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,UAAU,EAAE,eAAS,CAAC,UAAU,CAAC,OAAO;aACzC,CAAC;YACF,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE;gBACrE,MAAM,CAAC,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;aACtD;YACD,eAAe,GAAG,iBAAO,CAAC,kBAAkB,CAAC,iBAAO,CAAC,6BAA6B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACrG;QACD,OAAO;YACL,EAAE,IAAA;YACF,OAAO,EAAE,gBAAgB,CAAC;gBACxB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,SAAS,CAAC,cAAc;gBAC9B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,wBAAwB;gBAC9D,OAAO,EAAE,eAAe;gBACxB,aAAa,EAAE,WAAW;gBAC1B,UAAU,YAAA;aACX,CAAC;SACH,CAAC;KACH;IACD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;AAC5E,CAAC;AAED,IAAM,eAAe,GAAG;IACtB,uBAAuB,yBAAA;IACvB,wBAAwB,0BAAA;IACxB,wBAAwB,0BAAA;IACxB,yBAAyB,2BAAA;CAC1B,CAAC;AAEF,kBAAe,eAAe,CAAC"}