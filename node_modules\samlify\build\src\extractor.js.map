{"version": 3, "file": "extractor.js", "sourceRoot": "", "sources": ["../../src/extractor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,yCAA2C;AAC3C,+BAA8C;AAC9C,qCAA4D;AAC5D,wDAAkC;AAClC,IAAM,GAAG,GAAG,kBAAS,CAAC;AAatB,SAAS,kBAAkB,CAAC,KAAK;IAC/B,OAAO,KAAK,CAAC,MAAM,CAAC,UAAC,WAAW,EAAE,IAAI;QACpC,IAAI,YAAY,GAAG,WAAW,CAAC;QAC/B,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,UAAU,EAAE;YACd,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YACvC,YAAY,GAAG,WAAW,GAAG,qCAA8B,QAAQ,QAAK,CAAC;SAC1E;QACD,IAAI,CAAC,UAAU,EAAE;YACf,YAAY,GAAG,WAAW,GAAG,4BAAqB,IAAI,OAAI,CAAC;SAC5D;QACD,OAAO,YAAY,CAAC;IACtB,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,SAAS,mBAAmB,CAAC,UAAU;IACrC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,YAAK,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC;KAC7B;IACD,IAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,SAAS,IAAI,OAAA,kBAAW,SAAS,MAAG,EAAvB,CAAuB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClF,OAAO,cAAO,OAAO,MAAG,CAAC;AAC3B,CAAC;AAEY,QAAA,kBAAkB,GAAoB;IACjD;QACE,GAAG,EAAE,SAAS;QACd,SAAS,EAAE,CAAC,cAAc,CAAC;QAC3B,UAAU,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,6BAA6B,CAAC;KACjF;IACD;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC;QACrC,UAAU,EAAE,EAAE;KACf;IACD;QACE,GAAG,EAAE,cAAc;QACnB,SAAS,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;QAC3C,UAAU,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;KACtC;IACD;QACE,GAAG,EAAE,sBAAsB;QAC3B,SAAS,EAAE,CAAC,cAAc,EAAE,sBAAsB,CAAC;QACnD,UAAU,EAAE,EAAE;KACf;IACD;QACE,GAAG,EAAE,WAAW;QAChB,SAAS,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;QACxC,UAAU,EAAE,EAAE;QACd,OAAO,EAAE,IAAI;KACd;CACF,CAAC;AAEF,gCAAgC;AACnB,QAAA,yBAAyB,GAAG;IACvC;QACE,GAAG,EAAE,KAAK;QACV,SAAS,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;QAC/C,UAAU,EAAE,CAAC,OAAO,CAAC;KACtB;IACD;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;QAC7D,UAAU,EAAE,CAAC,OAAO,CAAC;KACtB;CACF,CAAC;AAEF,gCAAgC;AACnB,QAAA,0BAA0B,GAAG;IACxC;QACE,GAAG,EAAE,KAAK;QACV,SAAS,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,YAAY,CAAC;QACrD,UAAU,EAAE,CAAC,OAAO,CAAC;KACtB;IACD;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;QACnE,UAAU,EAAE,CAAC,OAAO,CAAC;KACtB;CACF,CAAC;AAEK,IAAM,mBAAmB,GAA0C,UAAA,SAAS,IAAI,OAAA;IACrF;QACE,GAAG,EAAE,YAAY;QACjB,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;QACtC,UAAU,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;QACzC,QAAQ,EAAE,SAAS;KACpB;IACD;QACE,GAAG,EAAE,UAAU;QACf,SAAS,EAAE,CAAC,UAAU,CAAC;QACvB,UAAU,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,CAAC;KAClE;IACD;QACE,GAAG,EAAE,UAAU;QACf,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,qBAAqB,EAAE,UAAU,CAAC;QACzE,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,SAAS;KACpB;IACD,IAAI;IACJ,mBAAmB;IACnB,uCAAuC;IACvC,mBAAmB;IACnB,KAAK;IACL;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClC,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,SAAS;KACpB;IACD;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC;QAC7C,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,SAAS;KACpB;IACD;QACE,GAAG,EAAE,cAAc;QACnB,SAAS,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC;QAC1C,UAAU,EAAE,CAAC,cAAc,EAAE,qBAAqB,EAAE,cAAc,CAAC;QACnE,QAAQ,EAAE,SAAS;KACpB;IACD;QACE,GAAG,EAAE,YAAY;QACjB,SAAS,EAAE,CAAC,WAAW,EAAE,oBAAoB,EAAE,WAAW,CAAC;QAC3D,KAAK,EAAE,CAAC,MAAM,CAAC;QACf,aAAa,EAAE,CAAC,gBAAgB,CAAC;QACjC,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,SAAS;KACpB;CACF,EAjDsF,CAiDtF,CAAC;AAjDW,QAAA,mBAAmB,uBAiD9B;AAEW,QAAA,mBAAmB,GAAoB;IAClD;QACE,GAAG,EAAE,SAAS;QACd,SAAS,EAAE,CAAC,eAAe,CAAC;QAC5B,UAAU,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,aAAa,CAAC;KAClD;IACD;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,eAAe,EAAE,QAAQ,CAAC;QACtC,UAAU,EAAE,EAAE;KACf;IACD;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,eAAe,EAAE,QAAQ,CAAC;QACtC,UAAU,EAAE,EAAE;KACf;IACD;QACE,GAAG,EAAE,cAAc;QACnB,SAAS,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC;QAC5C,UAAU,EAAE,EAAE;KACf;IACD;QACE,GAAG,EAAE,WAAW;QAChB,SAAS,EAAE,CAAC,eAAe,EAAE,WAAW,CAAC;QACzC,UAAU,EAAE,EAAE;QACd,OAAO,EAAE,IAAI;KACd;CACF,CAAC;AAEW,QAAA,oBAAoB,GAAoB;IACnD;QACE,GAAG,EAAE,UAAU;QACf,SAAS,EAAE,CAAC,gBAAgB,CAAC;QAC7B,UAAU,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,cAAc,CAAC;KAClD;IACD;QACE,GAAG,EAAE,QAAQ;QACb,SAAS,EAAE,CAAC,gBAAgB,EAAE,QAAQ,CAAC;QACvC,UAAU,EAAE,EAAE;KACf;IACD;QACE,GAAG,EAAE,WAAW;QAChB,SAAS,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC;QAC1C,UAAU,EAAE,EAAE;QACd,OAAO,EAAE,IAAI;KACd;CACF,CAAC;AAEF,SAAgB,OAAO,CAAC,OAAe,EAAE,MAAM;IAE7C,IAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IAEnD,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,MAAW,EAAE,KAAK;;QACtC,uBAAuB;QACvB,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QACtB,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAClC,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACpC,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAC/B,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,sBAAsB;QACtB,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1B,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QAE1C,0DAA0D;QAC1D,IAAI,SAAS,GAAG,OAAO,CAAC;QAExB,4CAA4C;QAC5C,2DAA2D;QAC3D,IAAI,QAAQ,EAAE;YACZ,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;SACjD;QAED,8BAA8B;QAC9B;;;;;;;;;WASG;QACH,IAAI,SAAS,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAnB,CAAmB,CAAC,EAAE;YAChD,IAAM,WAAW,GAAG,SAAS;iBAC1B,GAAG,CAAC,UAAA,IAAI;gBACP,0CAA0C;gBAC1C,OAAO,UAAG,kBAAkB,CAAC,IAAI,CAAC,YAAS,CAAC;YAC9C,CAAC,CAAC;iBACD,IAAI,CAAC,KAAK,CAAC,CAAC;YAEf,6BACK,MAAM,gBACR,GAAG,IAAG,IAAA,cAAI,EAAC,IAAA,cAAM,EAAC,WAAW,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,UAAC,CAAO,IAAK,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,CAAC,CAAC,MAAM,CAAC,kBAAQ,CAAC,CAAC,OAC1F;SACH;QACD,iCAAiC;QAEjC,IAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAChD,IAAM,cAAc,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAEvD,2EAA2E;QAC3E;;;;;;;;UAQE;QACF,IAAI,KAAK,IAAI,aAAa,EAAE;YAC1B,8BAA8B;YAC9B,IAAM,SAAS,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAM,cAAc,GAAG,UAAG,SAAS,SAAG,SAAS,CAAE,CAAC;YAClD,IAAM,WAAW,GAAG,IAAA,cAAM,EAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACjD,yDAAyD;YACzD,IAAM,gBAAgB,GAAG,IAAA,cAAM,EAAC,cAAc,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,UAAC,CAAO,IAAK,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,CAAC;YACrF,8BAA8B;YAC9B,IAAM,UAAU,GAAG,kBAAkB,CAAC,CAAC,IAAA,cAAI,EAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;YAC/E,IAAM,mBAAmB,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAC5D,IAAM,gBAAc,GAAG,UAAG,UAAU,SAAG,mBAAmB,CAAE,CAAC;YAC7D,8DAA8D;YAC9D,IAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,UAAA,IAAI;gBAC1C,IAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC3D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC3B,IAAM,WAAW,GAAG,IAAA,cAAM,EAAC,gBAAc,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,UAAC,CAAO,IAAK,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,CAAC,CAAC;oBAClF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC5B,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;qBACvB;oBACD,OAAO,WAAW,CAAC;iBACpB;gBACD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,IAAM,WAAW,GAAG,IAAA,cAAM,EAAC,gBAAc,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,UAAC,CAAO,IAAK,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,CAAC;oBAC9E,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC5B,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;qBACvB;oBACD,OAAO,WAAW,CAAC;iBACpB;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;YACH,cAAc;YACd,IAAM,GAAG,GAAG,IAAA,mBAAS,EAAC,gBAAgB,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;YAChE,6BACK,MAAM,gBACR,GAAG,IAAG,GAAG,OACV;SAEH;QACD,uDAAuD;QACvD;;;;;;;UAOE;QACF,IAAI,QAAQ,EAAE;YACZ,IAAM,IAAI,GAAG,IAAA,cAAM,EAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAC1C,IAAI,KAAK,GAA6B,IAAI,CAAC;YAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;aAC5B;YACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,CAAC,CAAC;aACrC;YACD,6BACK,MAAM,gBACR,GAAG,IAAG,KAAK,OACZ;SACH;QAED,2BAA2B;QAC3B;;;;;;UAME;QACF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAM,QAAQ,GAAG,IAAA,cAAM,EAAC,SAAS,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,CAAC,CAAC;YACrE,IAAM,YAAU,GAAG,UAAG,kBAAkB,CAAC,CAAC,IAAA,cAAI,EAAC,SAAS,CAAC,CAAC,CAAC,SAAG,cAAc,CAAE,CAAC;YAC/E,IAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,IAAY;gBAChD,IAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAChD,IAAM,MAAM,GAAG,IAAA,cAAM,EAAC,YAAU,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,UAAC,CAAM,EAAE,CAAO;oBAChE,CAAC,CAAC,IAAA,mBAAS,EAAC,CAAC,CAAC,IAAI,EAAE,EAAC,MAAM,EAAE,OAAO,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;oBAClD,OAAO,CAAC,CAAC;gBACX,CAAC,EAAE,EAAE,CAAC,CAAC;gBACP,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,6BACK,MAAM,gBACR,GAAG,IAAG,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,OAC1E;SACH;QACD,yBAAyB;QACzB;;;;;;UAME;QACF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,IAAM,QAAQ,GAAG,UAAG,SAAS,SAAG,cAAc,CAAE,CAAC;YACjD,IAAM,eAAe,GAAG,IAAA,cAAM,EAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,UAAC,CAAO,IAAK,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,CAAC;YAC9E,6BACK,MAAM,gBACR,GAAG,IAAG,eAAe,CAAC,CAAC,CAAC,OACzB;SACH;QACD,uBAAuB;QACvB;;;;;;UAME;QACF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,IAAI,cAAc,GAA+C,IAAI,CAAC;YACtE,IAAM,IAAI,GAAG,IAAA,cAAM,EAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAC1C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrB,IAAM,QAAQ,GAAG,iBAAU,SAAS,SAAG,cAAc,MAAG,CAAC;gBACzD,cAAc,GAAG,IAAA,cAAM,EAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;aAC9C;YACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,CAAO,IAAK,OAAA,CAAC,CAAC,UAAU,EAAZ,CAAY,CAAC;qBACpD,GAAG,CAAC,UAAC,CAAO,IAAK,OAAA,CAAC,CAAC,UAAW,CAAC,SAAS,EAAvB,CAAuB,CAAC,CAAC;aAC9C;YACD,6BACK,MAAM,gBACR,GAAG,IAAG,cAAc,OACrB;SACH;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;AAET,CAAC;AAjMD,0BAiMC"}