{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["../../src/validator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,SAAS,UAAU,CACjB,YAAgC,EAChC,eAAmC,EACnC,KAA8B;IAA9B,sBAAA,EAAA,SAAyB,CAAC,EAAE,CAAC,CAAC;IAG9B,IAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IAEvB,IAAI,CAAC,YAAY,IAAI,CAAC,eAAe,EAAE;QACrC,kHAAkH;QAClH,OAAO,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;QAC1G,OAAO,IAAI,CAAC;KACb;IAED,IAAI,cAAc,GAAgB,IAAI,CAAC;IACvC,IAAI,iBAAiB,GAAgB,IAAI,CAAC;IAEpC,IAAA,KAAA,OAAsC,KAAK,IAAA,EAA1C,cAAc,QAAA,EAAE,iBAAiB,QAAS,CAAC;IAElD,IAAI,YAAY,IAAI,CAAC,eAAe,EAAE;QACpC,cAAc,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;QACxC,OAAO,CAAC,cAAc,GAAG,cAAc,IAAI,CAAC,GAAG,CAAC;KACjD;IACD,IAAI,CAAC,YAAY,IAAI,eAAe,EAAE;QACpC,iBAAiB,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,GAAG,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KACtD;IAED,cAAc,GAAG,IAAI,IAAI,CAAC,YAAa,CAAC,CAAC;IACzC,iBAAiB,GAAG,IAAI,IAAI,CAAC,eAAgB,CAAC,CAAC;IAE/C,OAAO,CACL,CAAC,cAAc,GAAG,cAAc,IAAI,CAAC,GAAG;QACxC,CAAC,GAAG,GAAG,CAAC,iBAAiB,GAAG,iBAAiB,CAC9C,CAAC;AAEJ,CAAC;AAGC,gCAAU"}