import{r as c}from"./pkgroll_create-require-040ba28b.js";import{constants as l}from"os";import"./suppress-warnings..js";import"module";if(c("@esbuild-kit/cjs-loader"),process.send){let r=function(s){process.send({type:"kill",signal:s}),process.listenerCount(s)===0&&process.exit(128+l.signals[s])};const t=["SIGINT","SIGTERM"];for(const s of t)process.on(s,r);const{listenerCount:n}=process;process.listenerCount=function(s){let e=Reflect.apply(n,this,arguments);return t.includes(s)&&(e-=1),e};const{listeners:o}=process;process.listeners=function(s){const e=Reflect.apply(o,this,arguments);return t.includes(s)?e.filter(i=>i!==r):e}}
